const express = require('express');

const router = express.Router();
const asyncHandler = require('express-async-handler');
const moment = require('moment');
const httpErrors = require('../lib/http-errors');
const premiumLib = require('../lib/premium');
const metricsLib = require('../lib/metrics');
const pricingConfigLib = require('../lib/pricing-config');
const countryLib = require('../lib/country');
const interestLib = require('../lib/interest');
const onboardingVideoConfigLib = require('../lib/onboarding-video-config');
const partnershipsLib = require('../lib/partnerships');
const cityLib = require('../lib/city');
const locationLib = require('../lib/location');
const constants = require('../lib/constants')

async function getSuperLikeConfig(user) {
  let rv = {};
  let discount = '';
  let sale_end_date;
  const sale = await premiumLib.getSuperLikeFlashSale(user);
  if (sale) {
    discount = `_discount_${sale.discount}`;
    sale_end_date = sale.saleEndDate;
  }

  let variant = '_v1';

  rv.super_like_product_ids = [
    'super_love_50' + variant,
    'super_love_12' + variant,
    'super_love_3' + variant,
  ];

  if (discount) {
    rv.super_like_discounted_product_ids = [
      'super_love_50' + discount + variant,
      'super_love_12' + discount + variant,
      'super_love_3' + discount + variant,
    ];
    rv.super_like_discount = sale.discount;
    rv.super_like_sale_end_date = sale_end_date;
  } else {
    rv.super_like_discounted_product_ids = null;
    rv.super_like_discount = null;
    rv.super_like_sale_end_date = null;
  }
  return rv;
}

module.exports = function () {
  router.get('/', asyncHandler(async (req, res, next) => {
    const { user } = req;
    let rv = {};

    // metrics
    rv.numUniqueUsersWhoPurchasedInfinity = metricsLib.getNumUniqueUsersWhoPurchasedInfinity();
    rv.numUniqueUsersWhoPurchasedSuperLike = metricsLib.getNumUniqueUsersWhoPurchasedSuperLike();
    rv.numUniqueUsersWhoPurchasedCoins = metricsLib.getNumUniqueUsersWhoPurchasedCoins();

    // experimental configs
    if (req.user.config) {
      for (const [key, value] of Object.entries(req.user.config)) {
        rv[key] = value;
      }
    }

    // finalized configs
    rv.allow_automated_chat = false;
    rv.show_segmented_sign_up = true;
    rv.show_skip_buttons = true;
    rv.show_continue_sign_up_option = false;
    rv.show_pricing_variant = false;
    rv.show_get_more_button = null;
    rv.show_social_proof = true;
    rv.show_orb = false;
    rv.change_boo_infinity_value_prop_text = 1;
    rv.change_send_dm_text = false;
    rv.more_recs_for_higher_levels = true;
    rv.show_boo_infinity_price_per_day = false;
    rv.show_boo_infinity_promise = true;
    rv.show_interest_num_followers = true;
    rv.show_post_icon_on_bottom = false;
    rv.show_telepathy_in_profile = 0;
    rv.show_100_coins_option = true;
    rv.add_double_tap_to_purchase_option = false;
    rv.free_telepathy = true;
    rv.change_flash_sale_time_limit = 1;
    rv.change_boo_infinity_value_prop = 4;
    rv.add_haptic_feedback_to_purchasing = true;
    rv.change_boo_infinity_save_text = false;
    rv.change_autoplay_timing_social_proof = false;
    rv.glow_middle_option = false;
    rv.show_match_stats_during_signup = true;
    rv.show_progress_bar_on_edit = true;
    rv.change_home_screen_to_social = false;
    rv.show_photo_tips = true;
    rv.show_mutual_interests_on_new_chat = true;
    rv.move_prompts_to_top_of_edit = true;
    rv.change_daily_limit_reached_popup = true;
    rv.show_expired_likes = true;
    rv.move_description_up_in_edit = true;
    rv.use_boo_infinity_gifs = false;
    rv.use_summoning_gif = true;
    rv.show_post_to_social_in_signup = true;
    rv.change_summoning_gif = true;
    rv.show_view_count_on_posts = false;
    rv.use_boo_infinity_gifs_round_2 = true;
    rv.show_local_time = true;
    rv.show_verify_profile_popup = true;
    rv.change_love_description = false;
    rv.change_save_text = true;
    rv.remove_summoning_gif = true;
    rv.change_verification_picture = false;
    rv.show_description_tips = true;
    rv.show_social_tooltip_after_daily_limit = false;
    rv.show_social_tooltip_tutorial = true;
    rv.get_all_contacts = true;
    rv.change_save_text_v2 = true;
    rv.move_value_page_first = false;
    rv.show_related_posts = true;
    rv.move_most_popular_to_3 = true;
    rv.non_english_sort_best_week = false;
    rv.non_english_sort_best_week_v2 = false;
    rv.show_12_sold_out = true;
    rv.make_12_purchasable = false;
    rv.show_similar_interests_v2 = true;
    rv.move_post_button = false;
    rv.remove_soulmate_slide = false;
    rv.show_activate_boo_infinity_quest = true;
    rv.move_delete_testimonials = false;
    rv.show_similar_interests_v3 = false;
    rv.change_boo_infinity_order = false;
    rv.social_ads = true;
    rv.ads_for_coins = true;
    rv.show_invite_quest = true;
    rv.social_ad_frequency_index = 10;
    rv.change_mbti_ghosts = false;
    rv.show_multiple_ads = true;
    rv.change_referral_text = false;
    rv.remove_coins_from_premium = true;
    rv.show_action_items = false;
    rv.default_following = false;
    rv.move_telepathy = 2;
    rv.change_mbti_ghosts_v2 = false;
    rv.show_boost_post_powerup = true;
    rv.go_to_home_after_quiz = false;
    rv.go_to_social_after_signup = false;
    rv.require_notifications_for_swiping = false;
    rv.use_ghost_verification_pose = false;
    rv.show_advanced_filters = false;
    rv.add_social_to_delete = true;
    rv.show_view_last_seen_powerup = true;
    rv.allow_sticker_pack_coin_purchase = true;
    rv.change_dimension_icon = false;
    rv.make_premium_awards = false;
    rv.change_soulmate_slide = false;
    rv.change_delete_button_color = true;
    rv.change_boo_infinity_subtitle = false;
    rv.show_6_sold_out = false;
    rv.show_bottom_icon_text = true;
    rv.show_waving_boo_infinity_animation = false;
    rv.change_activate_to_continue = true;
    rv.show_premium_popup_on_app_open_v1 = false;
    rv.change_mbti_ghosts_v3 = true;
    rv.change_activate_to_continue_on_profile = false;
    rv.make_translation_premium = false;
    rv.new_karma_system = false;
    rv.change_verification_pose = true;
    rv.show_flash_sale_on_every_open = true;
    rv.add_side_bar = true;
    rv.show_flash_sale_timer = true;
    rv.remove_profile_from_bottom_add_create = true;
    rv.add_sale_banner = true;
    rv.remove_12_sold_out = true;
    rv.coin_rewards_for_social = false;
    rv.first_message_karma_award = true;
    rv.boo_infinity_purchase_animation = false;
    rv.liveness_verification = true;
    rv.show_new_match_tutorial = true;
    rv.use_bunny_cdn = true;
    rv.rate_app_after_15 = true;
    rv.show_ads_for_swipes = false;
    rv.pulsing_glow = false;
    rv.show_photo_toast = false;
    rv.sign_up_interests_mandatory = true;
    rv.social_tutorial = true;
    rv.verify_to_message = true;
    rv.rive_boo_infinity_animations = true;
    rv.lifestyle_in_signup = true;
    rv.improve_delete_account = true;
    rv.show_coins_social_proof = true;
    rv.see_who_viewed_profile = true;
    rv.for_you_algo = false;
    rv.boo_infinity_full_screen_popup = true;
    rv.coins_flash_sale = true;
    rv.super_love_design_consistency = false;
    rv.new_coins_animation = true;
    rv.lava_rate_app = true;
    rv.change_leveled_up_number_format = true;
    rv.coins_design_consistency = true;
    rv.exclude_3_super_love_flash_sale = true;
    rv.full_screen_coins = true;
    rv.full_screen_super_love = true;
    rv.refund_disclaimer = false;
    rv.boo_infinity_shimmer_prices = false;
    rv.slow_signup_animations = true;
    rv.use_number_formatting = true;
    rv.verify_to_post_comment = true;
    rv.immediate_account_deletion = false;
    rv.super_love_design_consistency_v2 = false;
    rv.exclude_1000_coins_from_sale = true;
    rv.larger_coins_animations = false;
    rv.super_love_sale_on_click = true;
    rv.show_onboarding_video = true;
    rv.show_network_debug_button = true;
    rv.god_mode = false;
    rv.change_super_love_animation = true;
    rv.profile_super_love = true;
    rv.hide_nested_comments = 2;
    rv.user_picture_in_super_love_purchase = false;
    rv.infp_soulmate_slide = false;
    rv.quotes_in_delete_account = true;
    rv.boo_infinity_sale_animation = false;
    rv.coins_sale_animation = true;
    rv.super_love_sale_animation = false;
    rv.remove_love_count = false;
    rv.friend_request_notification = true;
    rv.remove_for_you_for_non_popular_languages = true;
    rv.social_premium = false;
    rv.show_boo_ai = true;
    rv.english_faq = true;
    rv.tailored_super_love_gender_animation = true;
    rv.vampire_werewolf_soulmate = false;
    rv.show_boo_ai_social = true;
    rv.show_boo_ai_tutorial_first_time = true;
    rv.gendered_boo_infinity_animations = true;
    rv.ai_icebreaker_tutorial = true;
    rv.show_interests_and_verified_signup_carousel = true;
    rv.change_boo_ai_purchase_animation = true;
    rv.boost_on_requests_page = false;
    rv.show_purchase_super_love = true;
    rv.see_who_viewed_in_requests = true;
    rv.read_receipts_in_new_match = false;
    rv.purchases_in_sidebar = false;
    rv.use_aws_images_v2 = true;
    rv.app_55 = true;
    rv.app_71 = false;
    rv.app_53 = true;
    rv.app_54 = true;
    rv.app_105 = true;
    rv.app_108 = false;
    rv.app_68 = false;
    rv.app_83 = false;
    rv.app_69 = false;
    rv.app_41 = false;
    rv.app_112 = false;
    rv.send_keywords_to_admob = (user.os == 'android');
    rv.app_95 = false;
    rv.app_39 = false;
    rv.app_44 = true;
    rv.app_49 = true;
    rv.app_122 = false;
    rv.app_40 = false;
    rv.app_98 = true;
    rv.app_126 = true;
    rv.app_52 = false;
    rv.app_132 = false;
    rv.app_149 = false;
    rv.app_151 = false;
    rv.app_138 = false;
    rv.app_152 = false;
    rv.app_123 = true;
    rv.app_131 = true;
    rv.app_159 = true;
    rv.app_150 = false;
    rv.app_107 = true;
    rv.app_164 = true;
    rv.app_125 = true;
    rv.app_114 = true;
    rv.app_120 = false;
    rv.app_213 = true;
    rv.app_163 = true;
    rv.app_194 = true;
    rv.app_178 = true;
    rv.app_142 = false;
    rv.app_175 = true;
    rv.post_visibility_score = false;
    rv.app_190 = true;
    rv.app_153 = true;
    rv.app_174 = false;
    rv.app_168 = false;
    rv.app_171 = false;
    rv.app_201 = false;
    rv.app_197 = false;
    rv.app_146 = true;
    rv.app_146_v2 = true;
    rv.app_226 = false;
    rv.app_220 = false;
    rv.app_221 = true;
    rv.app_188 = true;
    rv.app_234 = true;
    rv.app_240 = false;
    rv.app_165 = false;
    rv.app_274 = false;
    rv.app_287 = true;
    rv.app_304 = true;
    rv.app_268 = true;
    rv.app_191 = true;
    rv.app_312 = false;
    rv.app_291 = true;
    rv.app_185 = false;
    rv.app_281 = false;
    rv.app_300 = true;
    rv.app_311 = false;
    rv.app_288 = true;
    rv.app_70 = false;
    rv.app_295 = true;
    rv.app_297 = true;
    rv.app_89 = true;
    rv.app_296 = false;
    rv.app_310 = true;
    rv.app_335 = true;
    rv.app_316 = true;
    rv.app_302 = false;
    rv.app_323 = false;
    rv.app_301 = false;
    rv.app_322 = true;
    rv.app_334 = false;
    rv.app_241 = true;
    rv.app_241_v2 = true;
    rv.app_351 = true;
    rv.app_325 = true;
    rv.app_378 = false;
    rv.app_236 = false;
    rv.app_376 = false;
    rv.app_355 = false;
    rv.app_381 = false;
    rv.app_309 = true;
    rv.app_365 = true;
    rv.app_319 = false;
    rv.app_237 = false;
    rv.app_367 = false;
    rv.app_421 = true;
    rv.app_321 = false;
    rv.app_414 = false;
    rv.app_406 = false;
    rv.app_418 = true;
    rv.app_416 = false;
    rv.app_374 = true;
    rv.app_366 = false;
    rv.app_371 = true;
    rv.show_distance_filter = true;
    rv.app_441 = false;
    rv.app_415 = false;
    rv.app_405 = false;
    rv.app_326 = false;
    rv.app_315 = true;
    rv.app_436 = true;
    rv.app_340 = true;
    rv.app_442 = false;
    rv.app_420 = true;
    rv.app_383 = true;
    rv.app_439 = false;
    rv.app_486 = true;
    rv.app_390 = true;
    rv.app_437 = true;
    rv.app_182 = true;
    rv.app_422 = true;
    rv.app_438 = false;
    rv.app_473 = false;
    rv.app_460 = false;
    rv.app_482 = true;
    rv.app_493 = false;
    rv.app_474 = false;
    rv.app_497 = false;
    rv.app_116 = false;
    rv.app_472 = true;
    rv.app_506 = true;
    rv.app_410 = true;
    rv.app_478 = false;
    rv.app_459 = true;
    rv.app_496 = false;
    rv.app_483 = true;
    rv.app_498 = true;
    rv.app_431_th = false;
    rv.app_431_vi = false;
    rv.app_431_ar = false;
    rv.app_431_ja = false;
    rv.app_431_ko = false;
    rv.app_431_ms = false;
    rv.app_510 = true;
    rv.app_495 = false;
    rv.app_514 = false;
    rv.app_518 = false;
    rv.app_463 = true;
    rv.app_463_v2 = true;
    rv.app_576 = true;
    rv.app_508 = false;
    rv.app_564 = true;
    rv.app_566 = true
    rv.app_566_v2 = true
    rv.app_544 = true
    rv.app_511 = false;
    rv.app_440 = false;
    rv.app_454 = true;
    rv.app_580 = false;
    rv.app_465 = true;
    rv.app_141 = true;
    rv.app_504 = true;
    rv.app_555 = false;
    rv.app_570 = true;
    rv.app_571 = false;
    rv.app_536 = true;
    rv.app_605 = true;
    rv.app_388 = true;
    rv.app_339 = false;
    rv.app_574 = false;
    rv.app_427 = false;
    rv.app_657 = false;
    rv.app_647 = false;
    rv.app_327 = false;
    rv.app_299 = true;
    rv.app_362 = false;
    rv.app_447 = true;
    rv.app_481 = false;
    rv.app_543 = true;
    rv.app_599 = true;
    rv.app_696 = false;
    rv.app_623 = true;
    rv.app_631 = false;
    rv.app_654 = true;
    rv.app_87 = true;
    rv.app_709 = true;
    rv.app_661 = true;
    rv.app_688 = true;
    rv.app_689 = false;
    rv.app_677 = false;
    rv.app_665 = false;
    rv.app_708 = true;
    rv.app_736 = true;
    rv.app_716 = false;
    rv.app_730 = false;
    rv.app_674 = false;
    rv.app_99 = false;
    rv.app_750 = true;
    rv.app_735 = false;
    rv.app_648 = true;
    rv.app_717 = false;
    rv.app_767 = true;
    rv.app_795 = true;
    rv.app_760 = true;
    rv.app_517 = true;
    rv.app_162 = false;
    rv.app_813 = process.env.NODE_ENV == 'beta'; // enable on beta, disable on prod
    rv.app_788 = true;
    rv.app_797 = true;
    rv.app_204 = false;
    rv.app_799 = false;
    rv.app_782 = false;
    rv.app_733 = true;
    rv.app_353 = false;
    rv.app_800 = false;
    rv.app_821 = false;
    rv.app_793 = true;
    rv.app_832 = true;
    rv.app_839 = false;
    rv.app_780 = true;
    rv.app_802 = true;
    rv.app_833 = false;
    rv.app_794 = false;
    rv.app_835 = false;
    rv.app_837 = true;
    rv.app_841 = true;
    rv.app_859 = false;
    rv.app_849 = false;
    rv.app_825 = true;
    rv.app_855 = true;
    rv.app_857 = false;
    rv.app_840 = true;
    rv.app_749 = true;
    rv.app_834 = true;
    rv.app_848 = true;
    rv.app_850 = true;
    rv.app_851 = false;
    rv.app_853 = false;
    rv.app_854 = false;
    rv.app_867 = true;
    rv.app_880 = true;
    rv.app_873 = false;
    rv.app_852 = false;
    rv.app_856 = true;
    rv.app_858 = true;
    rv.app_877 = true;
    rv.app_798 = false;
    rv.app_860 = false;
    rv.app_879 = 0;
    rv.app_875 = true;

    if (user.os == 'android') {
      rv.app_827 = true;
    }

    if (rv.use_storekit1 == undefined) {
      if (user.versionAtLeast('1.13.98')) {
        rv.use_storekit1 = false;
      } else {
        // disable storekit2 due to appsflyer revenue attribution issue
        rv.use_storekit1 = true;
      }
    }

    // app_763: set to true to enable nose challenge, false to enable yoti
    rv.app_763 = false;

    // note: the below is not ideal because some users have app_763 assigned from prior experiment
    /*
    if (user.config?.app_763 != undefined) {
      rv.app_763 = user.config.app_763;
    }
    */

    if (['United Kingdom','Australia','New Zealand'].some(x => [user.ipData?.country, user.country, user.actualCountry, locationLib.getCountryNameFromTimezone(user.timezone)].includes(x))) {
      rv.use_college = true;
    } else {
      rv.use_college = false;
    }

    if (cityLib.isTeleportRestricted(user)) {
      rv.teleportSameCountry = true;
    }

    {
      const interestSlides = interestLib.getOnboardingInterestsCarousel(user.interestNames, user.locale);
      if (interestSlides.some(x => x.name == 'gaming') && moment().diff(user.createdAt, 'days') < 14) {
        rv.app_650 = true;
      } else {
        rv.app_650 = false;
      }
    }

    // special override for derek for app_511
    if (user._id == 'V06GdMcaKhRmeJH9n0eEEogmLxJ3') {
      rv.app_511 = user.config.app_511;
    }

    const isPremium = premiumLib.isPremium(user);

    rv.app_350 = isPremium;

    let partnerships = partnershipsLib.getPartnershipData(req.ip);
    if (partnerships?.length) {
      rv.partnerships = true;
    }

    if (user.versionAtLeast('1.13.36') && user.signupCountry == 'Philippines') {
      rv.send_likes_verified_only = true;
    }

    if (isPremium) {
      rv.send_likes_verified_only = true;
    }

    if (countryLib.shouldRemoveCountryFilter(user)) {
      rv.removeCountryFilter = true;
    } else {
      rv.removeCountryFilter = false;
    }

    rv.premium_awards = [
      'goat',
      'coin_philanthropy',
      'ignite',
    ];

    if (moment().diff(user.birthday, 'years') <= 23) {
      rv.pricing_variant_number = 1;
    } else {
      rv.pricing_variant_number = null;
    }

    // device language feed
    const popularLanguages = metricsLib.getPopularLanguages();
    if (popularLanguages.includes(user.deviceLanguage) || user.isConfigTrue('default_user_to_device_language_feed')) {
      rv.show_device_language_feed = true;
    } else {
      rv.show_device_language_feed = false;
    }

    if (countryLib.group1Names.includes(user.signupCountry)) {
      rv.global_only_premium = true;
    } else {
      rv.global_only_premium = false;
    }

    const onboardingVideoConfig = onboardingVideoConfigLib.getOnboardingVideoConfig(user);
    if (onboardingVideoConfig != null) {
      rv.show_onboarding_video_all_languages = onboardingVideoConfig;
    }
    rv.onboarding_video_locales = onboardingVideoConfigLib.getAvailableVideoLocales();

    const onboardingVideoV2Config = onboardingVideoConfigLib.getOnboardingVideoV2Config(user);
    if (onboardingVideoV2Config != null) {
      rv.onboarding_video_v2 = onboardingVideoV2Config;
    }
    rv.onboarding_video_v2_locales = onboardingVideoConfigLib.getAvailableVideoV2Locales();

    const onboardingVideoFriendsConfig = onboardingVideoConfigLib.getOnboardingVideoFriendsConfig(user);
    if (onboardingVideoFriendsConfig != null) {
      rv.onboarding_video_friends = onboardingVideoFriendsConfig;
    }
    rv.onboarding_video_friends_locales = onboardingVideoConfigLib.getAvailableVideoFriendsLocales();

    rv.onboarding_video_url = onboardingVideoConfigLib.getOnboardingVideoUrl(user);

    // temp fix
    if (!rv.onboarding_video_v2) {
      rv.show_onboarding_video_all_languages = false;
    }

    rv.popular_languages = popularLanguages;

    // product ids
    const boo_infinity_1_week = 'boo_infinity_1_week';
    const boo_infinity_1_month = 'boo_infinity_1_month';
    const boo_infinity_3_months = 'boo_infinity_3_months';
    const boo_infinity_6_months = 'boo_infinity_6_months';
    const boo_infinity_12_months = 'boo_infinity_12_months';
    const boo_infinity_lifetime = 'boo_infinity_lifetime';

    let discount = '';
    const premiumFlashSale = premiumLib.getPremiumFlashSale(user);
    if (premiumFlashSale) {
      discount = `_discount_${premiumFlashSale.discount}`;
    }

    let variant = '';
    if (moment().diff(user.birthday, 'years') <= 23) {
      variant = '_variant_1';
    } else {
      if (user.preferences.dating.length > 0 && user.preferences.friends.length == 0) {
        variant = '_variant';
      }
      if (user.preferences.dating.length == 0 && user.preferences.friends.length > 0) {
        variant = '_variant_1';
      }
    }

    let product_ids = [
      boo_infinity_1_month + variant,
      boo_infinity_3_months + discount + variant,
      boo_infinity_6_months + discount + variant,
    ];
    if (user.versionAtLeast('1.11.31')) {
      product_ids = [
        boo_infinity_1_month + variant,
        boo_infinity_3_months + discount + variant,
        boo_infinity_6_months + discount + variant,
        boo_infinity_12_months + discount + variant,
      ];
    }
    if (user.versionAtLeast('1.11.33')) {
      product_ids = [
        boo_infinity_12_months + discount + variant,
        boo_infinity_6_months + discount + variant,
        boo_infinity_3_months + discount + variant,
        boo_infinity_1_month + variant,
      ];
    }
    if (user.versionAtLeast('1.11.36')) {
      product_ids = [
        boo_infinity_6_months + discount + variant,
        boo_infinity_3_months + discount + variant,
        boo_infinity_1_month + variant,
        boo_infinity_lifetime,
      ];
    }
    if (user.versionAtLeast('1.11.48')) {
      product_ids = [
        boo_infinity_12_months + discount + variant,
        boo_infinity_6_months + discount + variant,
        boo_infinity_3_months + discount + variant,
        boo_infinity_1_month + variant,
        boo_infinity_lifetime,
      ];
    }

    // pricing experiments
    let isPricingExperimentActive = false;
    let version;
    let variantNewFormat = '';
    let variantNewFormatWithX3 = '';
    if (moment().diff(user.birthday, 'years') <= 23) {
      variantNewFormat = '_x2';
      variantNewFormatWithX3 = '_x3';
    } else {
      if (user.preferences.dating.length > 0 && user.preferences.friends.length == 0) {
        variantNewFormat = '_x1';
        variantNewFormatWithX3 = '_x1';
      }
      if (user.preferences.dating.length == 0 && user.preferences.friends.length > 0) {
        variantNewFormat = '_x2';
        variantNewFormatWithX3 = '_x2';
      }
    }
    let variantToUse = variantNewFormat;
    const config = pricingConfigLib.getPricingConfig(user);
    if (config) {

      if (config.baseline > 1) {
        version = `_v${config.baseline}`;
      }
      if (config.variant > 1) {
        isPricingExperimentActive = true;
        let key;
        if (user.os == 'android') {
          key = `infinity_price_v${config.variant}`;
        }
        if (user.os == 'ios') {
          key = `ios_infinity_price_v${config.variant}`;
        }
        if (key && user.config[key] == true) {
          version = `_v${config.variant}`;
        }
      }

      if (version) {
        let discount = '';
        const premiumFlashSale = premiumLib.getPremiumFlashSale(user);
        if (premiumFlashSale) {
          discount = `_d${premiumFlashSale.discount}`;
        }

        if (user.os == 'ios' && version == '_v3') {
          // on ios v3, college has a new variant x3
          variantToUse = variantNewFormatWithX3;
        }
        product_ids = [
          `infinity_m12${version}${variantToUse}${discount}`,
          `infinity_m6${version}${variantToUse}${discount}`,
          `infinity_m3${version}${variantToUse}${discount}`,
          `infinity_m1${version}${variantToUse}`,
          `infinity_lifetime${version}`,
        ];
        if (user.os == 'ios' && version == '_v2') {
          // handle typo in product id
          product_ids[4] = 'infinity_lifetime_V2';
        }
        if (user.os == 'ios' && version == '_v3' && variantToUse == '_x2') {
          // ios v3_x2 products begin with "Infinity_"
          product_ids = [
            `Infinity_m12${version}${variantToUse}${discount}`,
            `Infinity_m6${version}${variantToUse}${discount}`,
            `Infinity_m3${version}${variantToUse}${discount}`,
            `Infinity_m1${version}${variantToUse}`,
            `infinity_lifetime${version}`,
          ];
        }
      }
    }

    // store product ids for easy reordering
    let product_id_12m;
    let product_id_6m;
    let product_id_3m;
    let product_id_1m;
    let product_id_lifetime;
    let product_id_1w;

    if (user.versionAtLeast('1.11.48')) {
      product_id_12m = product_ids[0];
      product_id_6m = product_ids[1];
      product_id_3m = product_ids[2];
      product_id_1m = product_ids[3];
      product_id_lifetime = product_ids[4];
    }

    if (user.versionAtLeast('1.13.48')) {
      // old order: 12/6/3/1/lifetime
      // new order: 12/1/3/6/lifetime
      product_ids = [
        product_id_12m,
        product_id_1m,
        product_id_3m,
        product_id_6m,
        product_id_lifetime,
      ]
    }

    if (user.versionAtLeast('1.13.57') && user.os == 'ios' && (!version || version == '_v2' || version == '_v3')) {
      const versionString = version || '_v1';
      product_id_1w = `infinity_w1${versionString}${variantToUse}`;
      // order: 12 months, 1 week, 1 month, 3 months, 6 months, lifetime.
      product_ids.splice(1, 0, product_id_1w);
    }
    /*
    if (user.os == 'android' && !isPricingExperimentActive && user.versionAtLeast('1.13.65') && user.isConfigTrue('app_476')) {
      product_id_1w = `infinity_w1${variantNewFormatWithX3}`;
      // order: 12 months, 1 week, 1 month, 3 months, 6 months, lifetime.
      product_ids.splice(1, 0, product_id_1w);
    }
    */

    if (user.versionAtLeast('1.13.95')) {
      product_ids = [
        product_id_1w,
        product_id_lifetime,
        product_id_12m,
        product_id_3m,
        product_id_1m,
        product_id_6m,
      ].filter(Boolean)
    }

    rv.product_ids = product_ids;

    let subscription_products = {
      boo_infinity: product_ids,
    };
    /*
    if (user.config.show_alternate_subscriptions) {
      subscription_products.unlimited_likes = ['unlimited_likes_1_month' + variant];
      subscription_products.unlimited_dms = ['unlimited_dms_1_month' + variant];
    }
    */
    /*
    if (user.isConfigTrue('god_mode')) {
      subscription_products = {
        boo_infinity_v2: [
          'infinity_w1_v5',
          'infinity_m1_v5',
          'infinity_m3_v5',
          'infinity_m6_v5',
        ],
        god_mode: [
          'premium_w1_v5',
          'premium_m1_v5',
          'premium_m3_v5',
          'lifetime_v5',
        ],
      }
    }
    */
    rv.subscription_products = subscription_products;

    if (user.versionAtLeast('1.12.16') && !user.versionAtLeast('1.13.6')) {
      let superLikeConfig = await getSuperLikeConfig(user);
      rv = {
        ...rv,
        ...superLikeConfig,
      };
    }

    if (user.os == 'ios' && !rv.use_pusher_ios) {
      // release to 100%
      rv.use_pusher_ios = true;
    }

    // release tenor to 100%
    rv.use_tenor = true;

    // collect consent for verification from users in Texas, Illinois, and Washington
    if (['TX','IL','WA'].includes(user.ipData?.region) || (user.country = 'United States' && ['Texas','Illinois','Washington'].includes(user.state))) {
      rv.app_515 = true;
    }

    if(user.createdAt > constants.getBackfillChatPerUserStateCutoffDate() || user.backfillChatPerUserStateComplete){
      rv.show_message_sort_options = true
    }

    if(user.versionAtLeast('1.13.86') && (user.createdAt > constants.getBackfillNumYourTurnChatsCutoffDate() || user.backfillNumYourTurnChatsComplete)){
      rv.app_687 = true
    } else {
      rv.app_687 = false;
    }

    // multiplier for postback revenue
    rv.app_765 = null; // null disables the multiplier

    rv.account_deletion_grace_period = user.accountDeletionGracePeriod || 30;

    res.json(rv);
  }));

  router.get('/coins', asyncHandler(async (req, res, next) => {
    let user = req.user;
    let rv = {};
    let discount = '';
    let sale_end_date;
    const sale = await premiumLib.getCoinsFlashSale(req.user);
    if (sale) {
      discount = `_discount_${sale.discount}`;
      sale_end_date = sale.saleEndDate;
    }
    rv.coins_product_ids = [
      '10000_coins',
      '4000_coins',
      '1000_coins',
      '100_coins',
    ];

    if (discount) {
      rv.coins_discounted_product_ids = [
        '10000_coins' + discount,
        '4000_coins' + discount,
        '1000_coins' + discount,
        '100_coins' + discount,
      ];
      rv.coins_discount = sale.discount;
      rv.coins_sale_end_date = sale_end_date;
    } else {
      rv.coins_discounted_product_ids = null;
      rv.coins_discount = null;
      rv.coins_sale_end_date = null;
    }

    res.json(rv);
  }));

  router.get('/superLike', asyncHandler(async (req, res, next) => {
    let rv = await getSuperLikeConfig(req.user);
    res.json(rv);
  }));

  router.get('/boosts', asyncHandler(async (req, res, next) => {
    let user = req.user;
    let rv = {};
    let discount = '';
    let sale_end_date;
    let variant = '';
    const sale = await premiumLib.getBoostsFlashSale(req.user);
    if (sale) {
      discount = `_d${sale.discount}`;
      sale_end_date = sale.saleEndDate;
    }


    if (moment().diff(user.birthday, 'years') <= 23) {
      variant = '_x3';
    }else if(user.preferences.dating.length > 0 && user.preferences.friends.length == 0){
      variant = '_x1'
    }else if(user.preferences.dating.length == 0  && user.preferences.friends.length > 0){
      variant = '_x2'
    }
    rv.boosts_product_ids = [
      'boost_10_v1' + variant,
      'boost_5_v1' + variant,
      'boost_1_v1' + variant,
    ];

    if (discount) {
      rv.boosts_discounted_product_ids = [
        'boost_10_v1' + variant + discount,
        'boost_5_v1' + variant + discount,
      ];
      rv.boosts_discount = sale.discount;
      rv.boosts_sale_end_date = sale_end_date;
    } else {
      rv.boosts_discounted_product_ids = null;
      rv.boosts_discount = null;
      rv.boosts_sale_end_date = null;
    }

    res.json(rv);
  }));

  router.put('/', asyncHandler(async (req, res, next) => {
    res.json({});
  }));

  router.get('/partnerships', asyncHandler(async (req, res, next) => {
    req.user.events.viewed_perks = 1;
    await req.user.save();

    let partnerships = partnershipsLib.getPartnershipData(req.ip);
    res.json({
      partnerships,
    });
  }));

  return router;
};
