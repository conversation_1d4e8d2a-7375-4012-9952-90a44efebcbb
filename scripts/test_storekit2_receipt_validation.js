const validator = require('../lib/app-store-connect');

const data = '*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';

(async () => {
  validator.validate(data, async (error, validatedData) => {
    if (error) {
      console.log(error, validatedData);
      return;
    }
    const options = {
      // Must set ignoreCanceled to avoid rejecting valid purchases
      // See: https://github.com/voltrue2/in-app-purchase/issues/342
      ignoreCanceled: true, // Apple ONLY
    };
    const purchaseData = validator.getPurchaseData(validatedData, options);

    console.log('validatedData', JSON.stringify(validatedData, null, 2));
    console.log('purchaseData', JSON.stringify(purchaseData, null, 2));
  });
})();
