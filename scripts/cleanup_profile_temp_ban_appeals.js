require('log-timestamp');
const mongoose = require('mongoose');
const ProfileTempBanAppeal = require('../models/profile-temp-ban-appeal');
const User = require('../models/user');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/test';

(async () => {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    const appealsToUpdate = await ProfileTempBanAppeal.aggregate([
      {
        $match: {
          decision: { $exists: false }
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'user',
          foreignField: '_id',
          as: 'userDoc'
        }
      },
      {
        $unwind: '$userDoc'
      },
      {
        $match: {
          // already unbanned
          'userDoc.shadowBanned': { $ne: true },
        }
      },
      {
        $project:{
          _id:1,
          user: "$userDoc._id",
        }
      }
    ]);

    console.log(`Found ${appealsToUpdate.length} appeals to dismiss`);
    console.log(JSON.stringify(appealsToUpdate));

    if (appealsToUpdate.length === 0) {
      console.log('No appeals need to be dismissed. Exiting.');
      return;
    }

    // Map user ids for bulk update
    const userIds = appealsToUpdate.map(doc => doc.user);

    if (userIds.length > 0) {
      await User.updateMany(
        { _id: { $in: userIds } },
        {
          $unset: {
            'accountRestrictions.profileTempBan': 1
          },
          $push: {
            banHistory: {
              action: 'ProfileTempBanAppealDismissed',
              date: Date.now(),
              notes: 'user was unbanned before appeal was reviewed',
            }
          }
        }
      );
      console.log(`Updated ${userIds.length} users`);
    }

    // Map appeal IDs for bulk update
    const appealIds = appealsToUpdate.map(doc => doc._id);

    await ProfileTempBanAppeal.updateMany(
      { _id: { $in: appealIds } },
      {
        $set: {
          decision: 'dismissed',
          notes: 'user was unbanned before appeal was reviewed'
        }
      }
    );

    console.log(`Updated ${appealIds.length} appeals`);

    console.log('All cleanup operations complete.');
  } catch (err) {
    console.log('Error during cleanup:', err);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
    process.exit(0);
  }
})();
