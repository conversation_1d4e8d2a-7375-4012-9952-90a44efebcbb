const path = require('path');
const cfsign = require('aws-cloudfront-sign');
const { getSigningParams } = require('../../lib/cloudfront');
const { s3, AWS_S3_BUCKET } = require('../../lib/s3');
const { sendEmailTemplateSES, translateEmail } = require('../../lib/email');
const DataRequestHistory = require('../../models/data-request-history');
const User = require('../../models/user');
const UserMetadata = require('../../models/user-metadata');
const Chat = require('../../models/chat');
const Question = require('../../models/question');
const Comment = require('../../models/comment');
const Story = require('../../models/story');
const PurchaseReceipt = require('../../models/purchase-receipt');
const CoinPurchaseReceipt = require('../../models/coin-purchase-receipt');
const SuperLikePurchaseReceipt = require('../../models/super-like-purchase-receipt');
const NeuronPurchaseReceipt = require('../../models/neuron-purchase-receipt');
const BoostPurchaseReceipt = require('../../models/boost-purchase-receipt');
const StripeReceipt = require('../../models/stripe-receipt');
const AdReceipt = require('../../models/ad-receipt');
const HideList = require('../../models/hide-list');
const PersonalityQuizResult = require('../../models/personality-quiz-result');
const Notification = require('../../models/notification');
const CoinTransaction = require('../../models/coin-transaction');
const DeleteAccountAttempt = require('../../models/delete-account-attempt');
const SavedQuestion = require('../../models/saved-question');
const OpenaiTransaction = require('../../models/openai-transaction');
const QuestionViewData = require('../../models/question-view-data');
const Action = require('../../models/action');
const Block = require('../../models/block');
const Follow = require('../../models/follow');
const HideOnSocial = require('../../models/hide-on-social');
const ProfileView = require('../../models/profile-view');
const ProfileVote = require('../../models/profile-vote');
const Award = require('../../models/award');
const BoostMetric = require('../../models/boost-metric');
const Interest = require('../../models/interest');
const LivenessChallenge = require('../../models/liveness-challenge');
const QuestionCandidate = require('../../models/question-candidate');
const Referral = require('../../models/referral');
const Report = require('../../models/report');
const PostReport = require('../../models/post-report');
const Translation = require('../../models/translation');
const { sendAutomatedReply } = require('../../lib/chat');
const { stringify } = require('flatted');
const { getStripeDataExport } = require('../../lib/stripe-data-export');
const archiver = require('archiver');
const { PassThrough, Readable } = require('stream');

const log = (msg) => console.log(`[data-export]: ${msg}`);

const config = {
  retryLimit: 3,
  bucketName: process.env.AWS_EXPIRY_S3_BUCKET || 'MOCK_S3_EXPIRY_BUCKET',
};

let dataExportRunning = false;
let processingUser = null;

const sendEmail = async (user, signedUrl, userId) => {
  const template = 'data-download-v1';
  const emailContent = [
    {
      name: 'HI_NAME',
      content: translateEmail({ phrase: 'Hello {{name}}', locale: user.locale }, { name: user.firstName }),
    },
    {
      name: 'MESSAGE',
      content: translateEmail({
        phrase: `We have compiled all the information associated with your Boo account. You can download it using the link below, valid for 7 days. If you have any questions, please reach out to us.`,
        locale: user.locale,
      }),
    },
    {
      name: 'DOWNLOAD_NOW',
      content: translateEmail({ phrase: 'Download Data', locale: user.locale }),
    },
    {
      name: 'DOWNLOAD_LINK',
      content: signedUrl,
    },
    {
      name: 'THANK_YOU',
      content: translateEmail({ phrase: 'Love,', locale: user.locale }),
    },
    {
      name: 'TEAM_BOO',
      content: translateEmail({ phrase: 'The Boo Team', locale: user.locale }),
    },
  ];

  const userData = await User.findById(userId);
  if (userData.versionAtLeast('1.13.64') && !userData.banNotice) {
    const dataDownloadLink = userData.versionAtLeast('1.13.73') ? `[Download Data](${signedUrl})` : signedUrl;
    const messageText = `${translateEmail({ phrase: `We have compiled all the information associated with your Boo account. You can download it using the link below, valid for 7 days.`, locale: user.locale })}\n\n${dataDownloadLink}`;
    await sendAutomatedReply(userData, messageText);
  } else {
    await sendEmailTemplateSES(userData, template, 'Your Boo Data is Now Available', emailContent);
  }
};

const getProfileAndAccount = async (USER_ID) => {
  let user = await User.findById(USER_ID)
    .select({
      _id: 0,
      firstName: 1,
      handle: 1,
      location: 1,
      country: 1,
      state: 1,
      city: 1,
      signupCountry: 1,
      googlePlayCountry: 1,
      'personality.mbti': 1,
      enneagram: 1,
      gender: 1,
      birthday: 1,
      age: 1,
      height: 1,
      ethnicities: 1,
      sexuality: 1,
      sexualityVisibility: 1,
      relationshipType: 1,
      datingSubPreferences: 1,
      relationshipStatus: 1,
      horoscope: 1,
      education: 1,
      work: 1,
      description: 1,
      moreAboutUser: 1,
      customPersonalityCompatibility: 1,
      audioDescription: 1,
      audioDescriptionWaveform: 1,
      audioDescriptionDuration: 1,
      prompts: 1,
      email: 1,
      phoneNumber: 1,
      interestNames: 1,
      interestPoints: 1,
      karma: 1,
      karmaTier: 1,
      languages: 1,
      awards: 1,
      timezone: 1,
      ipData: 1,
      preferences: 1,
      preferencesModifiedAt: 1,
      profileModifiedAt: 1,
      recommendationsExhaustedAt: 1,
      incomingRequestsPreferences: 1,
      fcmToken: 1,
      fcmTokenUpdatedAt: 1,
      pictures: 1,
      createdAt: 1,
      searchable: 1,
      hidden: 1,
      hideQuestions: 1,
      hideComments: 1,
      hideHoroscope: 1,
      hideLocation: 1,
      hideProfileViews: 1,
      hideCity: 1,
      hideReadReceipts: 1,
      premiumExpiration: 1,
      productIdPurchased: 1,
      boostExpiration: 1,
      boostDurationMinutes: 1,
      appVersion: 1,
      darkMode: 1,
      vibrationsDisabled: 1,
      messagesTheme: 1,
      changeHomeScreenToSocial: 1,
      dataSaver: 1,
      hideMyFollowerCount: 1,
      hideMyAwards: 1,
      hideMyKarma: 1,
      hideMyAge: 1,
      socialPreferences: 1,
      socialPreferencesActivated: 1,
      customFeeds: 1,
      pushNotificationSettings: 1,
      wingmanSettings: 1,
      aiSettings: 1,
      security: 1,
      termsAcceptedDate: 1,
      termsAcceptedHistory: 1,
      'verification.status': 1,
      'verification.updatedAt': 1,
      'verification.pictures': 1,
      'verification.rejectionReason': 1,
      hiddenContacts: 1,
      hideFromKeywords: 1,
      hideFromNearby: 1,
      hiddenInterests: 1,
      advertisingId: 1,
      optOutOfAdTargeting: 1,
      autoplay: 1,
      stripeCustomerId: 1,
      os: 1,
      osVersion: 1,
      phoneModel: 1,
      deviceId: 1,
      deviceIdHistory: 1,
      webDeviceId: 1,
      appDeviceId: 1,
      isPhysicalDevice: 1,
      deviceLanguage: 1,
      locale: 1,
      countryLocale: 1,
      socialFeedLanguage: 1,
      signupSource: 1,
      webSignupCategory: 1,
      webSignupPage: 1,
      webFirstVisitCategory: 1,
      webFirstVisitPage: 1,
      webFirstVisitReferringDomain: 1,
      webFirstVisitReferringURL: 1,
      approveAllFollowers: 1,
      autoFollowLikes: 1,
      autoFollowMatches: 1,
      numSuperLikes: 1,
      numBooAINeurons: 1,
      howDidYouHearAboutUs: 1,
      'metrics.numReferralsMade': 1,
      'metrics.numLikesSent': 1,
      'metrics.numPassesSent': 1,
      'metrics.numApprovalsSent': 1,
      'metrics.numRejectionsSent': 1,
      'metrics.numUnmatchesSent': 1,
      'metrics.numBlocksSent': 1,
      'metrics.numMessagesSent': 1,
      'metrics.numMatches': 1,
      'metrics.numMatchesFirstMessageSent': 1,
      'metrics.numMatchesReplySent': 1,
      'metrics.numMatchesBothMessaged': 1,
      'metrics.numDMSent': 1,
      'metrics.numDMSentFromSwiping': 1,
      'metrics.numDMSentFromSocial': 1,
      'metrics.numDMSentFromStories': 1,
      'metrics.numSuperLikesPurchased': 1,
      'metrics.numSuperLikesSent': 1,
      'metrics.numNeuronsPurchased': 1,
      'metrics.numNeuronsUsed': 1,
      'metrics.numSessions': 1,
      'metrics.numEngagedSessions': 1,
      'metrics.numSecondsEngagementTime': 1,
      'metrics.avgSecondsEngagementTime': 1,
      'metrics.numDeleteAccountAttempts': 1,
      'metrics.numDeleteAccountAttemptsCancelled': 1,
      'metrics.numFeedbackSubmitted': 1,
      'metrics.numPurchases': 1,
      'metrics.numCoinPurchases': 1,
      'metrics.numCoinsPurchased': 1,
      'metrics.numStripePurchases': 1,
      'metrics.numRefunds': 1,
      'metrics.revenueRefunded': 1,
      'metrics.revenue': 1,
      'metrics.coinRevenue': 1,
      'metrics.numSuperLikePurchases': 1,
      'metrics.superLikeRevenue': 1,
      'metrics.numNeuronPurchases': 1,
      'metrics.neuronRevenue': 1,
      'metrics.stripeRevenue': 1,
      'metrics.saleRevenue': 1,
      'metrics.numSalePurchases': 1,
      'metrics.lastSeen': 1,
      'metrics.lastSeenAndroid': 1,
      'metrics.lastSeenIos': 1,
      'metrics.lastSeenWeb': 1,
      'metrics.numFollowers': 1,
      'metrics.numFollowing': 1,
      'metrics.numFollowRequests': 1,
      'metrics.numProfileViews': 1,
      'metrics.numAwardsSent': 1,
      'metrics.numQuestions': 1,
      'metrics.numComments': 1,
      'metrics.numStories': 1,
      'metrics.numPostLikesSent': 1,
      'metrics.numPostUnlikesSent': 1,
      'metrics.numPostShares': 1,
      'metrics.numPostClicks': 1,
      'metrics.numSecondsReadingOnFeed': 1,
      'metrics.numSecondsReadingComments': 1,
      'metrics.retention': 1,
      'metrics.activeDates': 1,
      'metrics.numCoinAdsWatched': 1,
      'metrics.numCoinsEarnedFromAds': 1,
      'metrics.numViewLastSeenPurchased': 1,
      'metrics.purchasedPremiumFrom': 1,
      'metrics.purchasedCoinsFrom': 1,
      'metrics.revivalUsedAt': 1,
      'metrics.numRevivalUsed': 1,
      'metrics.numBoostUsed': 1,
      'metrics.numLiftOffUsed': 1,
      'events.finished_signup': 1,
      'events.enterDeleteAccountFlow': 1,
      'events.enterCoinsPage': 1,
      'events.enterSuperLovePage': 1,
      'events.enterSocialPage': 1,
      'events.referralLinkCreated': 1,
      'events.enterInvitePage': 1,
      'events.sharePersonality': 1,
      'events.tap_dimension_icon': 1,
      'events.change_dimension': 1,
      'events.viewed_faq': 1,
      'events.open_boo_ai': 1,
      'events.viewed_perks': 1,
      'appsflyer.appsflyer_id': 1,
      'appsflyer.payload.af_adset_id': 1,
      'appsflyer.payload.af_ad_type': 1,
      'appsflyer.payload.retargeting_conversion_type': 1,
      'appsflyer.payload.network': 1,
      'appsflyer.payload.is_first_launch': 1,
      'appsflyer.payload.af_click_lookback': 1,
      'appsflyer.payload.click_time': 1,
      'appsflyer.payload.match_type': 1,
      'appsflyer.payload.af_channel': 1,
      'appsflyer.payload.af_viewthrough_lookback': 1,
      'appsflyer.payload.campaign_id': 1,
      'appsflyer.payload.lat': 1,
      'appsflyer.payload.install_time': 1,
      'appsflyer.payload.af_c_id': 1,
      'appsflyer.payload.media_source': 1,
      'appsflyer.payload.ad_event_id': 1,
      'appsflyer.payload.af_siteid': 1,
      'appsflyer.payload.af_status': 1,
      'appsflyer.payload.referrer_gclid': 1,
      'appsflyer.payload.af_ad_id': 1,
      'appsflyer.payload.af_reengagement_window': 1,
      'appsflyer.payload.af_ad': 1,
      'appsflyer.payload.status': 1,
      'appsflyer.payload.af_message': 1,
      'appsflyer.payload.adgroup_id': 1,
      'appsflyer.payload.is_fb': 1,
      'appsflyer.payload.is_paid': 1,
      'appsflyer.payload.ad_id': 1,
      'appsflyer.payload.adset_id': 1,
      'appsflyer.payload.is_mobile_data_terms_signed': 1,
      'appsflyer.payload.af_dp': 1,
      'appsflyer.payload.event': 1,
      'appsflyer.payload.clickid': 1,
      'appsflyer.payload.event_id': 1,
      'appsflyer.payload.af_sub_siteid': 1,
      'appsflyer.payload.is_incentivized': 1,
      'appsflyer.payload.af_ip': 1,
      'appsflyer.payload.advertising_id': 1,
      'appsflyer.payload.af_ref': 1,
      'appsflyer.payload.af_os': 1,
      'appsflyer.payload.is_retargeting': 1,
      'appsflyer.payload.af_model': 1,
      'appsflyer.payload.af_pmod_lookback_window': 1,
      'appsflyer.payload.af_ua': 1,
      'appsflyer.payload.af_campaign_type': 1,
      'appsflyer.payload.af_os_version': 1,
      'appsflyer.payload.af_sub1': 1,
      'appsflyer.payload.af_lang': 1,
      'appsflyer.payload.af_vt_pmod_lookback_window': 1,
      'appsflyer.payload.webFirstVisitReferringURL': 1,
      'appsflyer.payload.shortlink': 1,
      'appsflyer.payload.webFirstVisitPage': 1,
      'appsflyer.payload.webDeviceId': 1,
      'appsflyer.payload.http_referrer': 1,
      'appsflyer.payload.partner': 1,
      'appsflyer.payload.request_id': 1,
      'appsflyer.payload.idfa': 1,
      'appsflyer.payload.af_inactivity_window': 1,
      'appsflyer.payload.deep_link_value': 1,
      'appsflyer.payload.fbclid': 1,
      'appsflyer.payload.sha1_idfa': 1,
      'appsflyer.payload.keyword_id': 1,
      'appsflyer.payload.af_keywords': 1,
      'appsflyer.payload.af_web_dp': 1,
      'appsflyer.payload.ad_ad_id': 1,
    })
    .lean();

  // user metadata
  const user_metadata = await UserMetadata.findOne({ user: USER_ID })
    .select({
      _id: 0,
      coins: 1,
      directMessageRewardReceived: 1,
      biographyRewardReceived: 1,
      pictureRewardReceived: 1,
      emailRewardReceived: 1,
      verifyProfileRewardReceived: 1,
      postInTwoDimensionsRewardReceived: 1,
      shareToSocialMediaRewardReceived: 1,
      rateAppRewardReceived: 1,
      detailedReviewRewardReceived: 1,
      enablePushNotificationsRewardReceived: 1,
      enableLocationRewardReceived: 1,
      enableTrackingRewardReceived: 1,
      howDidYouHearAboutUsRewardReceived: 1,
      cancelDeleteAccountRewardReceived: 1,
      firstDimensionPosted: 1,
      numLoginRewardsReceived: 1,
      loginRewardReceivedDate: 1,
      qodRewardsReceived: 1,
      translationRewardsReceived: 1,
      enneagramRewardReceived: 1,
      audioDescriptionRewardReceived: 1,
      quizAnswerRewardReceived: 1,
      profileCommentRewardReceived: 1,
      picturesRewardTier: 1,
      promptsRewardTier: 1,
      interestsRewardTier: 1,
      sendDMRewardTier: 1,
      postQuestionsRewardTier: 1,
      postStoriesRewardTier: 1,
      sharePostsRewardTier: 1,
      day0LoginRewardReceived: 1,
      day1LoginRewardReceived: 1,
      day2LoginRewardReceived: 1,
      day3LoginRewardReceived: 1,
      day4LoginRewardReceived: 1,
      day5LoginRewardReceived: 1,
      day6LoginRewardReceived: 1,
      directMessagesSent: 1,
      coinsSpentOnDirectMessages: 1,
    })
    .lean();

  user = {
    ...user,
    ...user_metadata,
  };

  return user;
};

const getChatsAndMessages = async (USER_ID) =>
  await Chat.aggregate([
    {
      $match: {
        users: USER_ID,
        deletedAt: null,
        bannedUsers: { $in: [null, [], USER_ID] },
      },
    },
    {
      $lookup: {
        from: 'messages',
        let: { chatId: '$_id' },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [{ $eq: ['$chat', '$$chatId'] }, { $eq: ['$sender', USER_ID] }],
              },
            },
          },
          {
            $sort: {
              createdAt: -1,
            },
          },
          {
            $project: {
              _id: 0,
              createdAt: 1,
              text: 1,
              image: 1,
              aspectRatio: 1,
              gif: 1,
              audio: 1,
              audioWaveform: 1,
              audioDuration: 1,
              video: 1,
              unsent: 1,
            },
          },
        ],
        as: 'messagesSentByMe',
      },
    },
    {
      $project: {
        _id: 0,
        createdAt: 1,
        dndPostFrom: 1,
        dndMessageFrom: 1,
        lastMessageTime: 1,
        numMessages: 1,
        pending: 1,
        messaged: 1,
        replied: 1,
        numMinutesUntilFirstReply: 1,
        initiatedByDM: 1,
        initiatedBySuperLike: 1,
        groupChat: 1,
        groupChatName: 1,
        messagesSentByMe: 1,
        isMuted: { $in: [USER_ID, { $ifNull: ['$muted', []] }] },
        isPinned: { $in: [USER_ID, { $ifNull: ['$pinned', []] }] },
        isHidden: { $in: [USER_ID, { $ifNull: ['$hidden', []] }] },
        isFirstMessageByMe: { $eq: [USER_ID, '$firstMessageBy'] },
        isPendingOnMe: { $eq: [USER_ID, '$pendingUser'] },
        numUnreadMessages: { $ifNull: [`$readReceipts.${USER_ID}.numUnreadMessages`, 0] },
        numSentMessages: { $ifNull: [`$readReceipts.${USER_ID}.numSentMessages`, 0] },
      },
    },
  ]);

const getPosts = async (USER_ID) =>
  await Question.find({ createdBy: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
      title: 1,
      text: 1,
      image: 1,
      altText: 1,
      aspectRatio: 1,
      audio: 1,
      audioWaveform: 1,
      audioDuration: 1,
      gif: 1,
      'poll.options': 1,
      interestName: 1,
      hashtags: 1,
      numComments: 1,
      numLikes: 1,
      language: 1,
      awards: 1,
    })
    .lean();

const getComments = async (USER_ID) =>
  await Comment.find({ createdBy: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
      text: 1,
      vote: 1,
      image: 1,
      aspectRatio: 1,
      audio: 1,
      audioWaveform: 1,
      audioDuration: 1,
      gif: 1,
      numComments: 1,
      numLikes: 1,
      language: 1,
      awards: 1,
    })
    .lean();

const getStories = async (USER_ID) =>
  await Story.find({ createdBy: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
      image: 1,
      textData: 1,
      visibility: 1,
      backgroundColor: 1,
      numUsersWhoViewed: 1,
      numUsersWhoLiked: 1,
    })
    .lean();

const getInfinityPurchases = async (USER_ID) =>
  await PurchaseReceipt.find({ user: USER_ID })
    .select({
      _id: 0,
      purchaseDate: 1,
      expirationDate: 1,
      renewalNumber: 1,
      productId: 1,
      currency: 1,
      price: 1,
      purchasedFrom: 1,
    })
    .lean();

const getCoinPurchases = async (USER_ID) =>
  await CoinPurchaseReceipt.find({ user: USER_ID })
    .select({
      _id: 0,
      purchaseDate: 1,
      productId: 1,
      currency: 1,
      price: 1,
      purchasedFrom: 1,
    })
    .lean();

const getSuperLikePurchases = async (USER_ID) =>
  await SuperLikePurchaseReceipt.find({ user: USER_ID })
    .select({
      _id: 0,
      purchaseDate: 1,
      productId: 1,
      currency: 1,
      price: 1,
    })
    .lean();

const getNeuronPurchases = async (USER_ID) =>
  await NeuronPurchaseReceipt.find({ user: USER_ID })
    .select({
      _id: 0,
      purchaseDate: 1,
      productId: 1,
      currency: 1,
      price: 1,
    })
    .lean();

const getBoostPurchases = async (USER_ID) =>
  await BoostPurchaseReceipt.find({ user: USER_ID })
    .select({
      _id: 0,
      purchaseDate: 1,
      productId: 1,
      currency: 1,
      price: 1,
      purchasedFrom: 1,
    })
    .lean();

const getWebPurchases = async (USER_ID) =>
  await StripeReceipt.find({ user: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
      product: 1,
      currency: 1,
      price: 1,
    })
    .lean();

const getAdsWatched = async (USER_ID) =>
  await AdReceipt.find({ user: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
      rewardItem: 1,
      rewardAmount: 1,
    })
    .lean();

const getHideList = async (USER_ID) =>
  await HideList.find({ userId: USER_ID })
    .select({
      _id: 0,
      emailsList: 1,
      phonesList: 1,
    })
    .lean();

const getPersonalityQuizHistory = async (USER_ID) =>
  await PersonalityQuizResult.find({ user: USER_ID })
    .select({
      _id: 0,
      date: 1,
      quiz: 1,
      mbti: 1,
    })
    .lean();

const getNotifications = async (USER_ID) =>
  await Notification.find({ user: USER_ID })
    .select({
      _id: 0,
      updatedAt: 1,
      postType: 1,
      notificationType: 1,
      seen: 1,
      numProfiles: 1,
    })
    .lean();

const getCoinTransactions = async (USER_ID) =>
  await CoinTransaction.find({ user: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
      transactionAmount: 1,
      newBalance: 1,
      description: 1,
    })
    .lean();

const getAccountDeleteAttempts = async (USER_ID) =>
  await DeleteAccountAttempt.find({ user: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
      feedback: 1,
      reason: 1,
    })
    .lean();

const getSavedPosts = async (USER_ID) =>
  await SavedQuestion.find({ user: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
    })
    .lean();

const getAiUsage = async (USER_ID) =>
  await OpenaiTransaction.find({ user: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
      useCase: 1,
      useCaseSubtype: 1,
      useCaseSubtype2: 1,
      outputLanguage: 1,
    })
    .lean();

const getPostViews = async (USER_ID) =>
  await QuestionViewData.find({ user: USER_ID })
    .select({
      _id: 0,
      updatedAt: 1,
      numClicks: 1,
      numSecondsReadingOnFeed: 1,
      numSecondsOnPost: 1,
      numSecondsReadingComments: 1,
      numShares: 1,
    })
    .lean();

const getSwipes = async (USER_ID) =>
  await Action.find({ from: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
      like: 1,
      pass: 1,
      block: 1,
      hide: 1,
    })
    .lean();

const getBlockList = async (USER_ID) =>
  await Block.find({ from: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
    })
    .lean();

const getFollows = async (USER_ID) =>
  await Follow.find({ from: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
      approved: 1,
    })
    .lean();

const getHiddenSocial = async (USER_ID) =>
  await HideOnSocial.find({ from: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
    })
    .lean();

const getProfileViews = async (USER_ID) =>
  await ProfileView.find({ from: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
    })
    .lean();

const getPersonalityVotes = async (USER_ID) =>
  await ProfileVote.find({ from: USER_ID })
    .select({
      _id: 0,
      mbti: 1,
      enneagram: 1,
      horoscope: 1,
    })
    .lean();

const getStoriesViewed = async (USER_ID) =>
  await Story.find({ usersWhoViewed: USER_ID })
    .select({
      _id: 0,
      storyCreatedAt: '$createdAt',
    })
    .lean();

const getStoriesLiked = async (USER_ID) =>
  await Story.find({ usersWhoLiked: USER_ID })
    .select({
      _id: 0,
      storyCreatedAt: '$createdAt',
    })
    .lean();

const getPostsLiked = async (USER_ID) =>
  await Question.find({ usersThatLiked: USER_ID })
    .select({
      _id: 0,
      postCreatedAt: '$createdAt',
    })
    .lean();

const getLikedComments = async (USER_ID) =>
  await Comment.find({ usersThatLiked: USER_ID })
    .select({
      _id: 0,
      commentCreatedAt: '$createdAt',
    })
    .lean();

const getAwardsGiven = async (USER_ID) =>
  await Award.find({ sender: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
      postType: 1,
      award: 1,
      anonymous: 1,
    })
    .lean();

const getBoostsUsed = async (USER_ID) =>
  await BoostMetric.find({ user: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
      durationMinutes: 1,
      boostExpiration: 1,
    })
    .lean();

const getInterestsSubmitted = async (USER_ID) =>
  await Interest.find({ createdBy: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
      name: 1,
      status: 1,
      pending: 1,
    })
    .lean();

const getVerificationAttempts = async (USER_ID) =>
  await LivenessChallenge.find({ user: USER_ID })
    .select({
      _id: 0,
      date: 1,
      frames: 1,
    })
    .lean();

const getQodSubmissions = async (USER_ID) =>
  await QuestionCandidate.find({ createdBy: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
      status: 1,
      text: 1,
      isAnonymous: 1,
      language: 1,
    })
    .lean();

const getReferrals = async (USER_ID) =>
  await Referral.find({ referredBy: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
    })
    .lean();

const getReportsMade = async (USER_ID) =>
  await Report.find({ reportedBy: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
      reason: 1,
      comment: 1,
    })
    .lean();

const getReportsReceived = async function(USER_ID) {
  const reports = await Report.find({ reportedUser: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
      reason: 1,
      status: 1,
      reportedBy: 1,
    })
    .lean();

  for (const report of reports) {
    if (!report.reportedBy) {
      report.reason = ['high_risk'];
    } else {
      report.reason = report.reason.map(function(x) {
        if (['Catfish/Scammer','Underage','Spam','Inappropriate Messages','Inappropriate Profile','Other'].includes(x)) {
          return x;
        }
        return 'high_risk';
      });
    }
    report.reportedBy = undefined;
  }

  return reports;
};

const getPostReports = async (USER_ID) =>
  await PostReport.find({ reportedBy: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
      reason: 1,
      explanation: 1,
    })
    .lean();

const getTranslationSuggestions = async (USER_ID) =>
  await Translation.find({ createdBy: USER_ID })
    .select({
      _id: 0,
      createdAt: 1,
      currentTranslation: 1,
      correctTranslation: 1,
      details: 1,
      language: 1,
      refImage: 1,
    })
    .lean();

const appendMediaFilesToArchive = async (archive, mediaFiles) => {
  // Dynamically compute timeout based on file size (5MB/s + 10s buffer, capped at 2min)
  const computeTimeoutMs = (sizeInBytes) => Math.min(120000, Math.max(30000, (sizeInBytes / 5_000_000) * 1000 + 10000));

  for (const file of mediaFiles) {
    let timeoutMs = 30000;

    try {
      const head = await s3.headObject({ Bucket: file.Bucket, Key: file.Key }).promise();
      const sizeInBytes = head.ContentLength || 0;
      timeoutMs = computeTimeoutMs(sizeInBytes);
    } catch (err) {
      log(`userId: ${file.userId} - Media file not found: ${file.Key}`);
      if (file.parent && file.field) {
        file.parent[file.field] = 'Not found';
      }
      // eslint-disable-next-line no-continue
      continue;
    }

    try {
      const s3Stream = s3.getObject({ Bucket: file.Bucket, Key: file.Key }).createReadStream();
      await Promise.race([
        new Promise((resolve, reject) => {
          s3Stream.on('error', (err) => {
            reject(err);
          });

          s3Stream.on('end', resolve);

          archive.append(s3Stream, { name: file.zipPath });
        }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error(`Failed to process file ${file.Key} within ${timeoutMs}ms`)), timeoutMs),
        ),
      ]);
    } catch (err) {
      log(`userId: ${file.userId} - Error processing file ${file.Key}: ${err.message}`);
      throw err;
    }
  }
};

const estimateExportTimeout = (mediaFiles) => {
  const TIMEOUTS = { '.mp4': 120_000, '.aac': 10_000 };
  const BASE = 30_000, DEF = 5_000;

  let total = BASE, videoCount = 0, audioCount = 0, otherCount = 0;

  for (const { Key } of mediaFiles) {
    const ext = path.extname(Key).toLowerCase();
    const t = TIMEOUTS[ext] ?? DEF;
    total += t;
    if (ext === '.mp4') videoCount++;
    else if (ext === '.aac') audioCount++;
    else otherCount++;
  }

  return { totalTimeoutMs: total, videoCount, audioCount, otherCount };
};

const streamZipToS3 = async ({ userId, outputKey, mediaFiles, jsonFiles }) => {
  const passThrough = new PassThrough();

  const uploadPromise = s3
    .upload({
      Bucket: config.bucketName,
      Key: outputKey,
      Body: passThrough,
      ContentType: 'application/zip',
    })
    .promise();

  const archive = archiver('zip', { zlib: { level: 9 } });

  archive.on('warning', (err) => {
    log(`userId: ${userId} - Archive warning: ${err.message}`);
  });

  archive.on('error', (err) => {
    log(`userId: ${userId} - Archive error: ${err.message}`);
    passThrough.destroy(err);
  });

  archive.pipe(passThrough);

  const { totalTimeoutMs, videoCount, audioCount, otherCount } = estimateExportTimeout(mediaFiles);
  log(`userId: ${userId} - Export will timeout within ${Math.round(totalTimeoutMs / 1000)}s ` +
    `(${videoCount} video, ${audioCount} audio, ${otherCount} other files)`);

  try {
    await Promise.race([
      (async () => {
        await appendMediaFilesToArchive(archive, mediaFiles);

        for (const { name, data } of jsonFiles) {
          const payload = typeof data === 'string' ? data : JSON.stringify(data, null, 2);
          archive.append(Readable.from([payload]), { name });
        }

        await new Promise((resolve, reject) => {
          archive.on('error', reject);
          archive.on('end', resolve);
          archive.on('finish', resolve);

          archive.finalize();
        });

        await uploadPromise;
      })(),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error(`Export timed out after ${totalTimeoutMs / 1000}s`)), totalTimeoutMs),
      ),
    ]);
  } catch (err) {
    log(`userId: ${userId} - Error during zip streaming: ${err.message}`);
    throw err;
  }
};

const addMediaFile = ({ mediaFiles, parent, field, index, userId }) => {
  const value = parent[field];
  if (!value || typeof value !== 'string') return;

  const ext = path.extname(value) || '';
  const zipPath = `media/${index}${ext}`;

  mediaFiles.push({
    Bucket: AWS_S3_BUCKET,
    Key: value,
    zipPath,
    parent,
    field,
    userId,
  });

  parent[field] = zipPath;
};

const processPendingRequest = async (dataRequest, dryRun) => {
  const startTime = Date.now();
  const USER_ID = dataRequest.userId;
  processingUser = USER_ID;
  try {
    let numMediaFiles = 0;
    const mediaFiles = [];
    const jsonFiles = [];

    const user = await getProfileAndAccount(USER_ID);
    if (Object.keys(user).length === 0) {
      log(`userId: ${USER_ID} - User not found, marking request as completed.`);
      dataRequest.status = 'completed';
      dataRequest.error = 'User not found';
      await dataRequest.save();
      return;
    }

    const chats = await getChatsAndMessages(USER_ID);
    const posts = await getPosts(USER_ID);
    const comments = await getComments(USER_ID);
    const stories = await getStories(USER_ID);
    const boo_infinity_purchases = await getInfinityPurchases(USER_ID);
    const coin_purchases = await getCoinPurchases(USER_ID);
    const super_love_purchases = await getSuperLikePurchases(USER_ID);
    const neuron_purchases = await getNeuronPurchases(USER_ID);
    const boost_purchases = await getBoostPurchases(USER_ID);
    const web_purchases = await getWebPurchases(USER_ID);
    const ads_watched = await getAdsWatched(USER_ID);
    const hide_list = await getHideList(USER_ID);
    const personality_quiz_history = await getPersonalityQuizHistory(USER_ID);
    const notifications = await getNotifications(USER_ID);
    const coin_transactions = await getCoinTransactions(USER_ID);
    const delete_account_attempts = await getAccountDeleteAttempts(USER_ID);
    const saved_posts = await getSavedPosts(USER_ID);
    const boo_ai_usage = await getAiUsage(USER_ID);
    const post_views = await getPostViews(USER_ID);
    const swipes = await getSwipes(USER_ID);
    const blocks = await getBlockList(USER_ID);
    const follows = await getFollows(USER_ID);
    const hidden_on_social = await getHiddenSocial(USER_ID);
    const profile_views = await getProfileViews(USER_ID);
    const personality_database_votes = await getPersonalityVotes(USER_ID);
    const stories_i_viewed = await getStoriesViewed(USER_ID);
    const stories_i_liked = await getStoriesLiked(USER_ID);
    const posts_i_liked = await getPostsLiked(USER_ID);
    const comments_i_liked = await getLikedComments(USER_ID);
    const awards_given = await getAwardsGiven(USER_ID);
    const boosts_used = await getBoostsUsed(USER_ID);
    const interests_submitted = await getInterestsSubmitted(USER_ID);
    const verification_attempts = await getVerificationAttempts(USER_ID);
    const question_of_the_day_submissions = await getQodSubmissions(USER_ID);
    const referrals = await getReferrals(USER_ID);
    const reports_made = await getReportsMade(USER_ID);
    const reports_received = await getReportsReceived(USER_ID);
    const postReports = await getPostReports(USER_ID);
    const translation_suggestions = await getTranslationSuggestions(USER_ID);
    const stripe_data_export = await getStripeDataExport(user.stripeCustomerId);

    {
      if (user.audioDescription) {
        addMediaFile({
          mediaFiles,
          parent: user,
          field: 'audioDescription',
          index: numMediaFiles++,
          userId: USER_ID,
        });
      }
      if (user.pictures && user.pictures.length) {
        for (let i = 0; i < user.pictures.length; i++) {
          addMediaFile({
            mediaFiles,
            parent: user.pictures,
            field: i,
            index: numMediaFiles++,
            userId: USER_ID,
          });
        }
      }
      if (user.verification.pictures && user.verification.pictures.length) {
        for (let i = 0; i < user.verification.pictures.length; i++) {
          addMediaFile({
            mediaFiles,
            parent: user.verification.pictures,
            field: i,
            index: numMediaFiles++,
            userId: USER_ID,
          });
        }
      }
      jsonFiles.push({ name: 'profile_and_account.json', data: user });
    }
    {
      for (let i = 0; i < chats.length; i++) {
        let chat = chats[i];
        chats[i].mutePostNotifications = chat && chat.dndPostFrom ? chat.dndPostFrom.filter((id) => USER_ID !== id).length > 0 : false;
        chats[i].muteMessageNotifications = chat && chat.dndMessageFrom ? chat.dndMessageFrom.filter((id) => USER_ID !== id).length > 0 : false;
        delete chats[i].dndPostFrom;
        delete chats[i].dndMessageFrom;
        if (chat.messagesSentByMe && chat.messagesSentByMe.length) {
          for (let j = 0; j < chat.messagesSentByMe.length; j++) {
            const msg = chat.messagesSentByMe[j];

            if (msg.unsent) {
              Object.assign(msg, {
                text: undefined,
                image: undefined,
                aspectRatio: undefined,
                gif: undefined,
                audio: undefined,
                audioWaveform: undefined,
                audioDuration: undefined,
                video: undefined,
              });
            }

            if (msg.image) {
              addMediaFile({
                mediaFiles,
                parent: msg,
                field: 'image',
                index: numMediaFiles++,
                userId: USER_ID,
              });
            }
            if (msg.audio) {
              addMediaFile({
                mediaFiles,
                parent: msg,
                field: 'audio',
                index: numMediaFiles++,
                userId: USER_ID,
              });
            }
            if (msg.video) {
              addMediaFile({
                mediaFiles,
                parent: msg,
                field: 'video',
                index: numMediaFiles++,
                userId: USER_ID,
              });
            }
          }
        }
      }
      jsonFiles.push({ name: 'chats_and_messages.json', data: chats });
    }
    {
      for (let i = 0; i < posts.length; i++) {
        if (posts[i].image) {
          addMediaFile({
            mediaFiles,
            parent: posts[i],
            field: 'image',
            index: numMediaFiles++,
            userId: USER_ID,
          });
        }
        if (posts[i].audio) {
          addMediaFile({
            mediaFiles,
            parent: posts[i],
            field: 'audio',
            index: numMediaFiles++,
            userId: USER_ID,
          });
        }
      }
      jsonFiles.push({ name: 'posts.json', data: posts });
    }
    {
      for (let i = 0; i < comments.length; i++) {
        if (comments[i].image) {
          addMediaFile({
            mediaFiles,
            parent: comments[i],
            field: 'image',
            index: numMediaFiles++,
            userId: USER_ID,
          });
        }
        if (comments[i].audio) {
          addMediaFile({
            mediaFiles,
            parent: comments[i],
            field: 'audio',
            index: numMediaFiles++,
            userId: USER_ID,
          });
        }
      }
      jsonFiles.push({ name: 'comments.json', data: comments });
    }
    {
      for (let i = 0; i < stories.length; i++) {
        if (stories[i].image) {
          addMediaFile({
            mediaFiles,
            parent: stories[i],
            field: 'image',
            index: numMediaFiles++,
            userId: USER_ID,
          });
        }
      }
      jsonFiles.push({ name: 'stories.json', data: stories });
    }
    {
      for (let i = 0; i < verification_attempts.length; i++) {
        if (verification_attempts[i].frames && verification_attempts[i].frames.length) {
          for (let j = 0; j < verification_attempts[i].frames.length; j++) {
            addMediaFile({
              mediaFiles,
              parent: verification_attempts[i].frames[j],
              field: 'key',
              index: numMediaFiles++,
              userId: USER_ID,
            });
          }
        }
      }
      jsonFiles.push({ name: 'verification_attempts.json', data: verification_attempts });
    }
    {
      for (let i = 0; i < translation_suggestions.length; i++) {
        if (translation_suggestions[i].refImage) {
          addMediaFile({
            mediaFiles,
            parent: translation_suggestions[i],
            field: 'refImage',
            index: numMediaFiles++,
            userId: USER_ID,
          });
        }
      }
      jsonFiles.push({ name: 'translation_suggestions.json', data: translation_suggestions });
    }

    /* -------------------------- */
    // California notice

    if ((user.ipData?.region == 'CA' && user.ipData?.countryCode == 'US') || (user.state == 'California' && user.country == 'United States')) {
      log(`userId: ${USER_ID} - Processing California notice for ${user.email}`);

      const attemptedVerification = user.verification?.pictures?.length || verification_attempts?.length;

      const category_a_identifiers = [];
      const category_b_customerRecords = [];
      const category_c_protectedClass = [];
      const category_d_commercial = [];
      const category_e_biometric = [];
      const category_f_electronic = [];
      const category_g_geolocation = [];
      const category_h_audiovisual = [];
      const category_i_professional = [];
      const category_j_education = [];
      const category_k_inferences = [];
      const category_spi = [];

      // category a
      if (user.firstName) {
        category_a_identifiers.push('name');
      }
      if (user.email) {
        category_a_identifiers.push('email address');
      }
      if (user.phoneNumber) {
        category_a_identifiers.push('phone number');
      }
      if (user.ipData?.ip) {
        category_a_identifiers.push('IP addresses');
      }
      if (user.deviceId) {
        category_a_identifiers.push('device ID');
      }

      // category b
      if (stripe_data_export?.stripe?.payment_methods?.length) {
        category_b_customerRecords.push('credit‑card last 4 digits');
        category_b_customerRecords.push('billing address');
      }
      if (user.education || user.work) {
        category_b_customerRecords.push('education/employment');
      }

      // category c
      if (user.gender) {
        category_c_protectedClass.push('gender');
      }
      if (user.ethnicities?.length) {
        category_c_protectedClass.push('ethnicity');
      }
      if (user.age) {
        category_c_protectedClass.push('age');
      }
      if (user.sexuality || (user.gender && user.preferences?.dating?.length)) {
        category_c_protectedClass.push('sexual orientation');
      }

      // category d
      if (boo_infinity_purchases?.length || coin_purchases?.length || super_love_purchases?.length || neuron_purchases?.length || boost_purchases?.length || web_purchases?.length) {
        category_d_commercial.push('purchase history');
      }

      // category e
      if (attemptedVerification) {
        category_e_biometric.push('processing of biometric information for unique identification');
      }

      // category f
      category_f_electronic.push('app usage');
      if (user.appsflyer) {
        category_f_electronic.push('information about your interaction with our advertisements (e.g., acquisition channel, campaign name)');
      }

      // category g
      if (user.ipData?.city || user.ipData?.region || user.ipData?.country || user.ipData?.location) {
        category_g_geolocation.push('approximate location from IP');
      }
      if (user.location) {
        category_g_geolocation.push('precise geolocation');
      }

      // category h
      if (user.pictures || user.audioDescription) {
        category_h_audiovisual.push('user-uploaded images, videos, and audio recordings');
      }

      // category i
      if (user.work) {
        category_i_professional.push('user-provided “work” field on profile');
      }

      // category j
      // none

      // category k
      // todo
      if (reports_received?.length) {
        category_k_inferences.push('trust & safety risk flag');
      }

      // category sensitive personal information (spi)
      if (attemptedVerification) {
        category_spi.push('processing of biometric information for unique identification');
      }
      if (user.location) {
        category_spi.push('precise geolocation');
      }

      // sources
      const sources = [
        'The consumer directly – information you submitted (account setup, etc).',
        'Your device/browser (via our sites/apps) – telemetry we collected when you used our services.',
      ];
      if (user.appsflyer) {
        sources.push('Data analytics providers — attribution reports linking your install/session to a campaign.');
      }
      if (user.stripeCustomerId) {
        sources.push('Service providers/contractors (collecting on our behalf) – Stripe collected billing information related to your purchases.');
      }
      if (boo_infinity_purchases?.length || coin_purchases?.length || super_love_purchases?.length || neuron_purchases?.length || boost_purchases?.length) {
        sources.push('Apple App Store / Google Play sent us purchase receipts for your in‑app orders.');
      }

      function formatExamples(examples) {
        return examples.join('; ') || 'none';
      }

      const section4 = `
4  Categories of Third Parties to Whom We Disclosed, Sold, or Shared Your PI
  * 4.1  Matrix of Disclosures for a Business Purpose
    * Cloud hosting & storage (service providers) — host/store our databases and files.
      * PI disclosed: Exactly the PI categories listed for you in Section 1 (i.e., the categories we actually collected about you are hosted with our cloud provider)
${ user.stripeCustomerId ? `
    * Payment processors (service providers) — process web payments.
      * PI disclosed: A. Identifiers (name, email)${stripe_data_export?.stripe?.payment_methods?.length ? '; B. § 1798.80(e) (e.g., billing address)' : ''}${web_purchases?.length ? '; D. Commercial information (e.g., transaction details)' : ''}
` : '' }
    * Customer support platforms (service providers) — handle our support inbox/tickets.
      * PI disclosed: A. Identifiers (name, email)
    * Messaging & email‑delivery (service providers) — send our emails.
      * PI disclosed: A. Identifiers (name, email)
  * 4.2  Categories of PI Sold (monetary or other valuable consideration)
    * None
  * 4.3  Categories of PI Shared for Cross‑Context Behavioural Advertising
    * None
`.replace(/\n{2,}/g, '\n').trim();   // collapses consecutive blank lines into a single newline and trims trailing/leading newlines

      const final_text = `
1  Categories of Personal Information We Collected About You
  * A. Identifiers - ${formatExamples(category_a_identifiers)}
  * B. Personal information in § 1798.80(e) - ${formatExamples(category_b_customerRecords)}
  * C. Protected‑class characteristics - ${formatExamples(category_c_protectedClass)}
  * D. Commercial information - ${formatExamples(category_d_commercial)}
  * E. Biometric information - ${formatExamples(category_e_biometric)}
  * F. Internet / electronic network activity - ${formatExamples(category_f_electronic)}
  * G. Geolocation data - ${formatExamples(category_g_geolocation)}
  * H. Audio/electronic/visual/thermal/olfactory/similar - ${formatExamples(category_h_audiovisual)}
  * I. Professional or employment‑related - ${formatExamples(category_i_professional)}
  * J. Nonpublic education information (FERPA) - ${formatExamples(category_j_education)}
  * K. Inferences - ${formatExamples(category_k_inferences)}
  * Sensitive Personal Information (SPI) - ${formatExamples(category_spi)}

2  Categories of Sources
${sources.map(x => '  * '.concat(x)).join('\n')}

3  Business or Commercial Purposes for Collection / Use / Sharing
  * Performing services - Maintain/serve your account; fulfill orders/transactions; verify customer information; process payments via our service providers; provide analytics and storage/hosting for our services.
  * Research & Development - Test and evaluate new features and UX.
  * Quality & Safety - Verify or maintain the quality or safety of our service/device, and improve, upgrade, or enhance it.
  * Security & Integrity - Detect and investigate security incidents; protect against malicious, deceptive, fraudulent, or illegal activity; ensure physical safety where applicable.
  * Debugging - Use app/device telemetry and error logs to identify and repair errors that impair existing intended functionality.

${section4}
`

      jsonFiles.push({ name: 'ccpa_cpra_access_response.txt', data: final_text });
    }

    /* -------------------------- */

    jsonFiles.push({ name: 'boo_infinity_purchases.json', data: boo_infinity_purchases });
    jsonFiles.push({ name: 'coin_purchases.json', data: coin_purchases });
    jsonFiles.push({ name: 'super_love_purchases.json', data: super_love_purchases });
    jsonFiles.push({ name: 'neuron_purchases.json', data: neuron_purchases });
    jsonFiles.push({ name: 'boost_purchases.json', data: boost_purchases });
    jsonFiles.push({ name: 'web_purchases.json', data: web_purchases });
    jsonFiles.push({ name: 'ads_watched.json', data: ads_watched });
    jsonFiles.push({ name: 'hide_list.json', data: hide_list });
    jsonFiles.push({ name: 'personality_quiz_history.json', data: personality_quiz_history });
    jsonFiles.push({ name: 'notifications.json', data: notifications });
    jsonFiles.push({ name: 'coin_transactions.json', data: coin_transactions });
    jsonFiles.push({ name: 'delete_account_attempts.json', data: delete_account_attempts });
    jsonFiles.push({ name: 'saved_posts.json', data: saved_posts });
    jsonFiles.push({ name: 'boo_ai_usage.json', data: boo_ai_usage });
    jsonFiles.push({ name: 'post_views.json', data: post_views });
    jsonFiles.push({ name: 'swipes.json', data: swipes });
    jsonFiles.push({ name: 'blocks.json', data: blocks });
    jsonFiles.push({ name: 'follows.json', data: follows });
    jsonFiles.push({ name: 'hidden_on_social.json', data: hidden_on_social });
    jsonFiles.push({ name: 'profile_views.json', data: profile_views });
    jsonFiles.push({ name: 'personality_database_votes.json', data: personality_database_votes });
    jsonFiles.push({ name: 'stories_i_viewed.json', data: stories_i_viewed });
    jsonFiles.push({ name: 'stories_i_liked.json', data: stories_i_liked });
    jsonFiles.push({ name: 'posts_i_liked.json', data: posts_i_liked });
    jsonFiles.push({ name: 'comments_i_liked.json', data: comments_i_liked });
    jsonFiles.push({ name: 'awards_given.json', data: awards_given });
    jsonFiles.push({ name: 'boosts_used.json', data: boosts_used });
    jsonFiles.push({ name: 'interests_submitted.json', data: interests_submitted });
    jsonFiles.push({ name: 'question_of_the_day_submissions.json', data: question_of_the_day_submissions });
    jsonFiles.push({ name: 'referrals.json', data: referrals });
    jsonFiles.push({ name: 'reports_made.json', data: reports_made });
    jsonFiles.push({ name: 'reports_received.json', data: reports_received });
    jsonFiles.push({ name: 'post_reports.json', data: postReports });
    jsonFiles.push({ name: 'stripe_data_export.json', data: stripe_data_export });

    if (!dryRun) {
      const outputKey = `data-export/${USER_ID}_${Date.now()}.zip`;
      await streamZipToS3({ outputKey, mediaFiles, jsonFiles, userId: USER_ID });

      let signedUrl = cfsign.getSignedUrl(
        `${process.env.WEB_DOMAIN}/${outputKey}`,
        getSigningParams(7),
      );

      if (process.env.TESTING) {
        signedUrl = 'MOCK_SIGNED_URL';
      }

      await sendEmail(user, signedUrl, USER_ID);

      dataRequest.status = 'completed';
      dataRequest.downloadKey = outputKey;
      dataRequest.emailedAt = new Date();
    }
  } catch (error) {
    log(`userId: ${dataRequest.userId} - Error while exporting data: ${error.message}`);
    if (!dryRun) {
      dataRequest.retryCount += 1;
      dataRequest.status = dataRequest.retryCount >= config.retryLimit ? 'failed' : 'pending';
      if (dataRequest.status === 'failed' || process.env.TESTING) {
        dataRequest.error = stringify(error);
      }
    }
  } finally {
    const durationSec = Number(((Date.now() - startTime) / 1000).toFixed(2));
    dataRequest.processingTimeSec = durationSec;
    await dataRequest.save();
    log(`userId: ${dataRequest.userId} - Data export ${dataRequest.status}, retry count ${dataRequest.retryCount} in ${durationSec} seconds`);
  }
};

const exportData = async (req, res, next) => {
  // always return 200 immediately: worker environment in AWS Elastic Beanstalk has a timeout
  if (!process.env.TESTING) {
    res.json({});
  }

  if (dataExportRunning) {
    log(`Data export job already running, skipping this job! Currently processing userId: ${processingUser}`);
    return;
  }

  dataExportRunning = true;
  try {
    const pendingDataRequests = await DataRequestHistory.find({ status: 'pending' }).sort({ _id: 1 });
    for (const dataRequest of pendingDataRequests) {
      await processPendingRequest(dataRequest);
    }
  } catch (error) {
    log(`Fatal error exporting data: ${error.message}`);
  } finally {
    dataExportRunning = false;
    if (process.env.TESTING) {
      // for test environments, return at end
      res.json({});
    }
  }
};

module.exports = { processPendingRequest, exportData };
