const moment = require('moment');
const Bottleneck = require('bottleneck');
const Chat = require('../../models/chat');
const Message = require('../../models/message');
const s3 = require('../../lib/s3');

let deleteJobRunning = false;

const limiter = new Bottleneck({
  maxConcurrent: 50,
});

async function task(chatId) {
  await s3.emptyS3Directory(`chats/${chatId}/`, 'deleteChatFiles');
}

async function deleteChats(req, res, next) {
  if (!process.env.TESTING) {
    // always return 200 immediately
    res.json({});
  }

  if (deleteJobRunning) {
    console.log('deleteChats job already running, skipping this job');
    return;
  }

  console.log('deleteChats job starting');
  deleteJobRunning = true;

  try {
    const cutoff = new Date('2025-09-04');
    const thirtyDaysAgo = new Date(Date.now() - 30*24*60*60*1000);

    const doomed = await Chat.find(
      { deletedAt: { $lte: thirtyDaysAgo } },
      '_id createdAt hasMediaFiles'
    ).lean();

    {
      // Build sets/lists once
      const allChatIdsToDelete = doomed.map(d => d._id);
      const s3ChatIds = doomed
        .filter(d => d.createdAt < cutoff || (d.createdAt >= cutoff && d.hasMediaFiles))
        .map(d => d._id);

      const s3Set = new Set(s3ChatIds);

      const batchSize = 10000;
      const numBatches = Math.ceil(allChatIdsToDelete.length / batchSize);
      console.log(
        `deleteChats job: processing ${allChatIdsToDelete.length} chats in ${numBatches} batches`
      );

      for (let i = 0; i < numBatches; i++) {
        const start = i * batchSize;
        const batch = allChatIdsToDelete.slice(start, start + batchSize);

        // Split batch by whether S3 work is needed
        const idsNeedingS3 = [];
        const idsNoS3 = [];
        for (const id of batch) (s3Set.has(id) ? idsNeedingS3 : idsNoS3).push(id);

        // 1) Kick S3 deletions (with limiter) for those that need it
        let s3SucceededIds = [];
        let s3FailedIds = [];
        if (idsNeedingS3.length) {
          const results = await Promise.allSettled(
            idsNeedingS3.map(id => limiter.schedule(() => task(id)))
          );

          // Partition successes/failures; keep alignment by index
          for (let j = 0; j < results.length; j++) {
            const r = results[j];
            const id = idsNeedingS3[j];
            if (r.status === 'fulfilled') s3SucceededIds.push(id);
            else {
              s3FailedIds.push(id);
              console.log(
                `deleteChats job: S3 delete failed for chatId=${id} in batch ${i + 1}/${numBatches}:`,
                r.reason
              );
            }
          }

          console.log(
            `deleteChats job: S3 batch ${i + 1}/${numBatches}: needed=${idsNeedingS3.length}, ok=${s3SucceededIds.length}, failed=${s3FailedIds.length}`
          );
        }

        // 2) Compose DB-deletion list:
        //    - IDs with no S3 work
        //    - IDs whose S3 deletion succeeded
        const idsToDeleteInDb = idsNoS3.concat(s3SucceededIds);

        // 3) Delete from DB (messages then chats) for the above IDs only
        if (idsToDeleteInDb.length) {
          {
            const result = await Message.deleteMany({ chat: { $in: idsToDeleteInDb } });
            console.log(
              `deleteChats job: DB messages deleted for ${result.deletedCount} documents (batch ${i + 1}/${numBatches})`
            );
          }
          {
            const result = await Chat.deleteMany({ _id: { $in: idsToDeleteInDb } });
            console.log(
              `deleteChats job: DB chats deleted for ${result.deletedCount} documents (batch ${i + 1}/${numBatches})`
            );
          }
        } else {
          console.log(`deleteChats job: no DB deletes in batch ${i + 1}/${numBatches} (all S3 failed?)`);
        }

        // 4) Optionally: persist failed IDs for a retry job
        if (s3FailedIds.length) {
          // e.g., push to a dead-letter queue / collection
          // await FailedChatDeletions.insertMany(s3FailedIds.map(_id => ({ _id, when: new Date() })));
          console.log(
            `deleteChats job: retained ${s3FailedIds.length} chatIds for S3 retry; DB not deleted for those`
          );
        }

        console.log(`deleteChats job: completed batch ${i + 1}/${numBatches}`);
      }

      console.log('deleteChats job: all batches processed');
    }

  } catch (error) {
    console.log(`deleteChats job: error: ${error}`);
  }

  deleteJobRunning = false;
  console.log('deleteChats job: finished');

  if (process.env.TESTING) {
    // for test environments, return at end
    res.json({});
  }
}

module.exports = {
  deleteChats,
};
