const mongoose = require('mongoose');
const connectionLib = require('../lib/connection');

const schema = new mongoose.Schema({
  createdAt: { type: Date, default: Date.now },
  mostRecentSearch: { type: Date },
  admin: { type: String, ref: 'User' },
  searchedUser: { type: String, ref: 'User' },
  numSearches: { type: Number, default: 0 },
});

schema.index({
  admin: 1,
  searchedUser: 1,
});

// Export schema =====================================================================================================================================================================
let conn = connectionLib.getEventsConnection() || mongoose;
module.exports = mongoose.model('AdminSearch', schema);
