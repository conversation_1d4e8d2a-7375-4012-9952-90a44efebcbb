const GeoJSON = require('mongoose-geojson-schema');
const moment = require('moment');
const cmp = require('semver-compare');
const mongoose = require('mongoose');
const { DateTime } = require('luxon');
const util = require('util');
const DeletedAccount = require('./deleted-account');
const Chat = require('./chat');
const Story = require('./story');
const FriendList = require('./friend-list');
const PersonalityQuizResult = require('./personality-quiz-result');
const Action = require('./action');
const Block = require('./block');
const UserMetadata = require('./user-metadata');
const ExclusionList = require('./exclusion-list');
const Report = require('./report');
const Follow = require('./follow');
const SavedQuestion = require('./saved-question');
const Question = require('./question');
const Comment = require('./comment');
const QuestionCandidate = require('./question-candidate');
const QuestionViewData = require('./question-view-data');
const Translation = require('./translation');
const ProfileView = require('./profile-view');
const S3DeletionMetric = require('../models/s3-deletion-metric');
const s3 = require('../lib/s3');
const basic = require('../lib/basic');
const { getCurrentDayResetTime, calculateViewableInDailyProfiles, getProfileModifiedAt } = require('../lib/basic');
const { enneagrams } = require('../lib/enneagram');
const { horoscopes, getHoroscope, getAge } = require('../lib/horoscope');
const { languageCodes } = require('../lib/languages');
const { ethnicities } = require('../lib/ethnicities');
const { validMbti } = require('../lib/personality');
const { validOs, rejectionReasons, getKarmaTiers, get_app_413_date } = require('../lib/constants');
const { extractValidWords } = require('../lib/text-search');
const { moreAboutUserChoices, relationshipStatusChoices, datingSubPreferencesChoices, sexualityChoices, relationshipTypeChoices } = require('../lib/moreAboutUser');
const genderPreferenceLib = require('../lib/gender-preference');
const metricsLib = require('../lib/metrics');
const countryLib = require('../lib/country');
const { getRegion } = require('../lib/country-regions');
const { cloudwatch } = require('../lib/cloudwatch');
const onboardingVideoConfigLib = require('../lib/onboarding-video-config');
const InterestPoint = require('../models/interest-point');
const { getNicheFromCampaign } = require('../lib/niche-campaigns');
const SuperLikeTransaction = require('../models/super-like-transaction');
const { insertScammerCleanup } = require('../lib/scammer-cleanup');

// added to avoid circular dependency
let sendSocketEventForDeleteChat = null;
let eligibleForVerification = null;
let unban = null;
let updateInterestRanks = null;
let preemptiveModerationForUserProfileText = null;

const defaultRetentionArray = [true].concat(new Array(90).fill(false));
const perDayMetricType = {
  0: { type: Number, default: 0 },
  1: { type: Number, default: 0 },
  2: { type: Number, default: 0 },
  3: { type: Number, default: 0 },
  4: { type: Number, default: 0 },
  5: { type: Number, default: 0 },
  6: { type: Number, default: 0 },
  7: { type: Number, default: 0 },
}

const userSchema = new mongoose.Schema({
  _id: { type: String, required: true },
  uid: { type: String },
  createdAt: { type: Date, default: () => new Date() },
  updatedAt: { type: Date, default: () => new Date().setHours(0, 0, 0, 0) },
  firstName: {
    type: String,
    default: '',
    trim: true,
    maxlength: 50,
  },
  handle: {
    type: String,
    unique: true,
    trim: true,
    sparse: true,
    minlength: 3,
    maxlength: 30,
    lowercase: true,
    validate: {
      validator(v) {
        return /^[a-z0-9._]+$/.test(v);
      },
      message: (props) => 'Handles must contain only letters, numbers, periods, and underscores.',
    },
  },
  searchable: { type: Boolean, default: true },
  hidden: { type: Boolean, default: false },
  hideQuestions: { type: Boolean, default: false },
  hideComments: { type: Boolean, default: false },
  hideHoroscope: { type: Boolean },
  hideLocation: { type: Boolean },
  hideProfileViews: { type: Boolean },
  viewableInDailyProfiles: { type: Boolean },
  darkMode: { type: Boolean },
  useMetricSystem: { type: Boolean },
  vibrationsDisabled: { type: Boolean },
  messagesTheme: { type: Number },
  changeHomeScreenToSocial: { type: Boolean },
  dataSaver: { type: Boolean },  // deprecated
  dataSaving: {
    type: {
      _id: false,
      profiles: {
        videos: { type: Boolean },
        pictures: { type: Boolean },
        firstPhotoOnly: { type: Boolean },
      },
      messages: {
        videos: { type: Boolean },
        pictures: { type: Boolean },
      },
      universe: {
        videos: { type: Boolean },
        pictures: { type: Boolean },
      },
    },
    default: undefined,
  },
  hideMyFollowerCount: { type: Boolean },
  hideMyAwards: { type: Boolean },
  hideMyKarma: { type: Boolean },
  hideMyAge: { type: Boolean },

  // the location used for matching
  location: mongoose.Schema.Types.Point,
  longitude: { type: Number },
  latitude: { type: Number },
  longitude2: { type: Number },
  latitude2: { type: Number },
  longitude3: { type: Number },
  latitude3: { type: Number },
  region: { type: String },
  countryGroup: { type: Number },
  countryCode: { type: String, trim: true, maxlength: 500 }, // iso2 country code
  country: { type: String, trim: true, maxlength: 500 },
  state: { type: String, trim: true, maxlength: 500 },
  city: { type: String, trim: true, maxlength: 500 },
  metro: { type: String },

  // actual location
  actualLocation: mongoose.Schema.Types.Point,
  actualCountryCode: { type: String, trim: true, maxlength: 500 },
  actualCountry: { type: String, trim: true, maxlength: 500 },
  actualState: { type: String, trim: true, maxlength: 500 },
  actualCity: { type: String, trim: true, maxlength: 500 },

  // teleport location
  teleportLocation: mongoose.Schema.Types.Point,
  teleportCountryCode: { type: String, trim: true, maxlength: 500 },
  teleportCountry: { type: String, trim: true, maxlength: 500 },
  teleportState: { type: String, trim: true, maxlength: 500 },
  teleportCity: { type: String, trim: true, maxlength: 500 },

  numUsersWithin5Miles: { type: Number },
  numUsersWithinCalculatedAt: { type: Date },

  signupCountry: { type: String, trim: true, maxlength: 500 },
  googlePlayCountry: { type: String, trim: true, maxlength: 500 },
  signupCountryIncorrect: { type: Boolean },

  personality: {
    date: { type: Date, default: null },
    quiz: {
      type: Map,
      of: Number,
      default: {},
    },
    mbti: {
      type: String,
      enum: [
        'ENFJ', 'ENFP', 'ENTJ', 'ENTP',
        'ESFJ', 'ESFP', 'ESTJ', 'ESTP',
        'INFJ', 'INFP', 'INTJ', 'INTP',
        'ISFJ', 'ISFP', 'ISTJ', 'ISTP',
        null,
      ],
    },
    EI: { type: Number, default: null },
    NS: { type: Number, default: null },
    FT: { type: Number, default: null },
    JP: { type: Number, default: null },
    TA: { type: Number, default: null },
  },
  enneagram: { type: String, enum: enneagrams },
  gender: { type: String, enum: ['male', 'female', 'non-binary'] },
  genderPreferenceHash: { type: String, enum: genderPreferenceLib.allGenderPreferenceHashes.concat([null]) },
  birthday: { type: Date, default: null },
  age: { type: Number },
  height: { type: Number },  // cm
  horoscope: { type: String, enum: horoscopes },
  education: {
    type: String,
    default: '',
    trim: true,
    maxlength: 50,
  },
  work: {
    type: String,
    trim: true,
    maxlength: 50,
  },
  description: {
    type: String,
    default: '',
    trim: true,
    maxlength: 10000,
  },
  originalFields: {
    description: { type: String },
    work: { type: String },
    education: { type: String },
    prompts: {
      type: [
        {
          _id: false,
          id: { type: String },
          prompt: { type: String },
          answer: { type: String },
        },
      ],
      default: undefined,
    },
  },
  keywords: [{ type: String }],
  relationshipStatus: {
    type: String, enum: relationshipStatusChoices,
  },
  datingSubPreferences: {
    type: String, enum: datingSubPreferencesChoices,
  },
  relationshipType: {
    type: String, enum: relationshipTypeChoices,
  },
  relationshipTypePopupClosed: { type: Boolean },
  datingPreferencesPopupClosed: { type: Boolean },
  datingPreferencesPopupClosedFromDiscover: { type: Boolean },
  moreAboutUser: {
    exercise: {
      type: String, enum: moreAboutUserChoices.exercise,
    },
    educationLevel: {
      type: String, enum: moreAboutUserChoices.educationLevel,
    },
    drinking: {
      type: String, enum: moreAboutUserChoices.drinking,
    },
    smoking: {
      type: String, enum: moreAboutUserChoices.smoking,
    },
    kids: {
      type: String, enum: moreAboutUserChoices.kids,
    },
    religion: {
      type: String, enum: moreAboutUserChoices.religion,
    },
  },
  spotify: {
    type: {
      _id: false,
      artists: [
        {
          _id: false,
          name: { type: String, maxlength: 10000 },
          picture: { type: String, maxlength: 10000 },
          link: { type: String, maxlength: 10000 },
        },
      ],
    },
    default: undefined,
  },
  customPersonalityCompatibility: {
    type: {
      _id: false,
      compatible: [{type: String}],
      potential: [{type: String}],
      challenging: [{type: String}],
    },
    default: undefined,
  },
  audioDescription: { type: String },
  audioDescriptionWaveform: { type: [{ type: Number }], default: undefined },
  audioDescriptionDuration: { type: Number },
  prompts: [
    {
      _id: false,
      id: { type: String },
      prompt: { type: String },
      answer: { type: String, trim: true, maxlength: 10000 },
    },
  ],
  crown: { type: Boolean, default: false },
  stickerPackPurchases: [{ type: String }],
  preferences: {
    minAge: {
      type: Number, min: 18, max: 200, default: 18,
    },
    maxAge: {
      type: Number, min: 18, max: 200, default: 200,
    },
    minHeight: { type: Number }, // cm
    maxHeight: { type: Number },
    personality: {
      type: [{ type: String, enum: validMbti }],
      default: validMbti,
    },
    dating: [{ type: String, enum: ['male', 'female', 'non-binary'] }],
    friends: [{ type: String, enum: ['male', 'female', 'non-binary'] }],
    local: { type: Boolean },
    global: { type: Boolean },
    showVerifiedOnly: { type: Boolean, default: false },
    showToVerifiedOnly: { type: Boolean },
    sameCountryOnly: { type: Boolean },
    showUsersOutsideMyRange: { type: Boolean, default: true },
    bioLength: { type: Number },
    countries: {
      type: [{ type: String }],
      default: undefined,
    },
    interests: {
      type: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Interest' }],
      default: undefined,
    },
    interestNames: {
      type: [{ type: String }],
      default: undefined,
    },
    enneagrams: {
      type: [{ type: String, enum: enneagrams }],
      default: undefined,
    },
    horoscopes: {
      type: [{ type: String, enum: horoscopes }],
      default: undefined,
    },
    languages: {
      type: [{ type: String, enum: languageCodes }],
      default: undefined,
    },
    ethnicities: {
      type: [{ type: String, enum: ethnicities }],
      default: undefined,
    },
    exercise: {
      type: [{ type: String, enum: moreAboutUserChoices.exercise }],
      default: undefined,
    },
    educationLevel: {
      type: [{ type: String, enum: moreAboutUserChoices.educationLevel }],
      default: undefined,
    },
    drinking: {
      type: [{ type: String, enum: moreAboutUserChoices.drinking }],
      default: undefined,
    },
    smoking: {
      type: [{ type: String, enum: moreAboutUserChoices.smoking }],
      default: undefined,
    },
    relationshipStatus: {
      type: [{ type: String, enum: relationshipStatusChoices }],
      default: undefined,
    },
    datingSubPreferences: {
      type: [{ type: String, enum: datingSubPreferencesChoices }],
      default: undefined,
    },
    relationshipType: {
      type: [{ type: String, enum: relationshipTypeChoices }],
      default: undefined,
    },
    kids: {
      type: [{ type: String, enum: moreAboutUserChoices.kids }],
      default: undefined,
    },
    religion: {
      type: [{ type: String, enum: moreAboutUserChoices.religion }],
      default: undefined,
    },
    // deprecated
    gender: {
      type: [{ type: String, enum: ['male', 'female', 'non-binary'] }],
      default: undefined,
    },
    purpose: {
      type: [{ type: String, enum: ['dating', 'friends'] }],
      default: undefined,
    },
    distance: {
      type: Number, min: 0, max: 12500, default: 12500,
    }, // miles
    distance2: { type: Number, min: 0, max: 100 },
    keywords: [{ type: String }],
    excludedInterestNames: { type: [{ type: String }], default: undefined },
    showUnspecified: {
      datingSubPreferences: { type: Boolean },
      relationshipStatus: { type: Boolean },
      relationshipType: { type: Boolean },
      sexuality: { type: Boolean },
      enneagrams: { type: Boolean },
      ethnicities: { type: Boolean },
      drinking: { type: Boolean },
      educationLevel: { type: Boolean },
      exercise: { type: Boolean },
      kids: { type: Boolean },
      religion: { type: Boolean },
      smoking: { type: Boolean },
    },
    sexuality: {
      type: [{type: String, enum: sexualityChoices }],
      default: undefined,
    },
  },
  incomingRequestsPreferences: {
    sameCountryOnly: { type: Boolean },
    matchSwipingPreferences: { type: Boolean },
    customActivated: { type: Boolean },
    custom: {
      type: {
        _id: false,
        dating: [{ type: String, enum: ['male', 'female', 'non-binary'] }],
        friends: [{ type: String, enum: ['male', 'female', 'non-binary'] }],
        genderPreferenceHash: { type: String },
        minAge: { type: Number, min: 18, max: 200 },
        maxAge: { type: Number, min: 18, max: 200 },
        countries: { type: [{ type: String }], default: undefined },
      },
      default: undefined,
    },
  },
  socialPreferences: {
    type: {
      _id: false,
      minAge: { type: Number, min: 18, max: 200 },
      maxAge: { type: Number, min: 18, max: 200 },
      showVerifiedOnly: { type: Boolean },
      personality: {
        type: [{ type: String, enum: validMbti }],
        default: undefined,
      },
      dating: {
        type: [{ type: String, enum: ['male', 'female', 'non-binary'] }],
        default: undefined,
      },
      friends: {
        type: [{ type: String, enum: ['male', 'female', 'non-binary'] }],
        default: undefined,
      },
      countries: {
        type: [{ type: String }],
        default: undefined,
      },
      enneagrams: {
        type: [{ type: String, enum: enneagrams }],
        default: undefined,
      },
      horoscopes: {
        type: [{ type: String, enum: horoscopes }],
        default: undefined,
      },
      minKarma: {
        type: Number,
        default: undefined,
      },
    },
    default: undefined,
  },
  socialPreferencesActivated: { type: Boolean },
  customFeeds: [{
    _id: false,
    feedName: { type: String },
    dating: {
      type: [{ type: String, enum: ['male', 'female', 'non-binary'] }],
      default: undefined,
    },
    friends: {
      type: [{ type: String, enum: ['male', 'female', 'non-binary'] }],
      default: undefined,
    },
    minAge: { type: Number, min: 18, max: 200 },
    maxAge: { type: Number, min: 18, max: 200 },
    showVerifiedOnly: { type: Boolean },
    relationship: { type: String, enum: ['everyone', 'friends', 'following'] },
    interestNames: {
      type: [{ type: String }],
      default: undefined,
    },
    keywords: {
      type: [{ type: String }],
      default: undefined,
    },
    excludeKeywords: {
      type: [{ type: String }],
      default: undefined,
    },
    countries: {
      type: [{ type: String }],
      default: undefined,
    },
    prioritizeNearby: { type: Boolean },
    prioritizeSameCountry: { type: Boolean },
    personality: {
      type: [{ type: String, enum: validMbti }],
      default: undefined,
    },
    enneagrams: {
      type: [{ type: String, enum: enneagrams }],
      default: undefined,
    },
    horoscopes: {
      type: [{ type: String, enum: horoscopes }],
      default: undefined,
    },
  }],
  pushNotificationSettings: {
    profileLikes: { type: Boolean, default: true },
    matches: { type: Boolean, default: true },
    messages: { type: Boolean, default: true },
    commentLikes: { type: Boolean, default: true },
    commentLikesMatches: { type: Boolean, default: true },
    commentLikesOtherSouls: { type: Boolean, default: true },
    commentReplies: { type: Boolean, default: true },
    commentRepliesMatches: { type: Boolean, default: true },
    commentRepliesOtherSouls: { type: Boolean, default: true },
    dailyPush: { type: Boolean, default: true },
    dailyFacts: { type: Boolean, default: true },
    questionOfTheDay: { type: Boolean, default: true },
    newSoulsNearby: { type: Boolean, default: true },
    followRequests: { type: Boolean, default: true },
    approvedFollowRequests: { type: Boolean, default: true },
    friendPosts: { type: Boolean, default: true },
    friendStories: { type: Boolean, default: true },
    email: { type: Boolean, default: true },
    sms: { type: Boolean, default: true },
    promotions: { type: Boolean, default: true },
  },
  wingmanSettings: {
    warnings: { type: Boolean, default: true },
  },
  aiSettings: {
    tone: { type: String },
    outputLanguage: { type: String },
  },
  scores: {
    numActionsReceived: { type: Number, default: 0 },
    descriptionAndPromptsLength: { type: Number, default: 0 },
    numPictures: { type: Number, default: 0 },
    pictureScore: { type: Number, default: 0 },
    verificationScore: { type: Number, default: 0 },
    descriptionScore: { type: Number, default: 0 },
    likeRatioScore: { type: Number, default: 20 },
    likeRatioScoreUpdatedAt: { type: Date },
    totalScore: { type: Number },
    decayedScore: { type: Number },
    totalScore2: { type: Number },
    decayedScore2: { type: Number },
  },
  preferencesModifiedAt: { type: Date },
  profileModifiedAt: { type: Date },
  recommendationsExhaustedAt: { type: Date },
  recentRecommendations: [ { type: String, ref: 'User' } ],
  recentCarrotRecommendations: [ { type: String, ref: 'User' } ],
  recentRecommendationsSavedAt: { type: Date },
  localLoops: {
    type: Map,
    of: Date,
    default: {},
  },
  countryLoops: {
    type: Map,
    of: Date,
    default: {},
  },
  countryGroupLoops: {
    type: Map,
    of: Date,
    default: {},
  },
  exclusionListFailed: { type: Boolean },
  startTimeOfExclusionListRecalculation: { type: Date },
  sizeOfExclusionUsedDuringFailure: { type: Number },
  metrics: {
    numReferralsMade: { type: Number, default: 0 },
    numActionsSent: { type: Number, default: 0 },
    numActionsReceived: { type: Number, default: 0 },
    numLikesSent: { type: Number, default: 0 },
    numPassesSent: { type: Number, default: 0 },
    numLocalLikesSent: { type: Number, default: 0 },
    numLocalPassesSent: { type: Number, default: 0 },
    numGlobalLikesSent: { type: Number, default: 0 },
    numGlobalPassesSent: { type: Number, default: 0 },
    numApprovalsSent: { type: Number, default: 0 },
    numRejectionsSent: { type: Number, default: 0 },
    numUnmatchesSent: { type: Number, default: 0 },
    numBlocksSent: { type: Number, default: 0 },
    numMessagesSent: { type: Number, default: 0 },
    numLikesReceived: { type: Number, default: 0 },
    numLikesReceivedVisible: { type: Number, default: 0 },
    numPassesReceived: { type: Number, default: 0 },
    numApprovalsReceived: { type: Number, default: 0 },
    numRejectionsReceived: { type: Number, default: 0 },
    numUnmatchesReceived: { type: Number, default: 0 },
    numBlocksReceived: { type: Number, default: 0 },
    numMessagesReceived: { type: Number, default: 0 },
    numNotifications: { type: Number, default: 0 },
    numNotificationErrors: { type: Number, default: 0 },
    numMatches: { type: Number, default: 0 },
    numMatchesFirstMessageSent: { type: Number, default: 0 },
    numMatchesFirstMessageReceived: { type: Number, default: 0 },
    numMatchesReplySent: { type: Number, default: 0 },
    numMatchesReplyReceived: { type: Number, default: 0 },
    numMatchesBothMessaged: { type: Number, default: 0 },
    numDMSent: { type: Number, default: 0 },
    numDMSentFromSwiping: { type: Number, default: 0 },
    numDMSentFromSocial: { type: Number, default: 0 },
    numDMSentFromStories: { type: Number, default: 0 },
    numDMReceived: { type: Number, default: 0 },
    numSuperLikesPurchased: { type: Number, default: 0 },
    numSuperLikesSent: { type: Number, default: 0 },
    numSuperLikesSentFree: { type: Number, default: 0 },
    numNeuronsPurchased: { type: Number, default: 0 },
    numNeuronsUsed: { type: Number, default: 0 },
    openaiCosts: { type: Number, default: 0 },
    numMinutesUntilFirstLikeReceived: { type: Number, default: null },
    numMinutesUntilFirstLikeReceivedVisible: { type: Number, default: null },
    numMinutesUntilFirstMatch: { type: Number, default: null },
    numMinutesUntilFirstMessageReceived: { type: Number, default: null },
    numMinutesUntilFirstPost: { type: Number, default: null },
    numMinutesUntilFinishSignup: { type: Number, default: null },
    numMinutesUntilFirstPurchase: { type: Number, default: null },
    numMinutesUntilVerified: { type: Number, default: null },
    numPersonalizedLikesSent: { type: Number, default: 0 },
    numPersonalizedPassesSent: { type: Number, default: 0 },
    currentDayResetTime: { type: Date, default: () => new Date() },
    swipeLimitResetTime: { type: Date },
    loginRewardReceivedTime: { type: Date },
    previousAction: { type: mongoose.Schema.Types.ObjectId, ref: 'Action' },
    numSessions: { type: Number, default: 0 },
    numEngagedSessions: { type: Number, default: 0 },
    numSecondsEngagementTime: { type: Number, default: 0 },
    avgSecondsEngagementTime: { type: Number, default: 0 },
    numDeleteAccountAttempts: { type: Number, default: 0 },
    numDeleteAccountAttemptsCancelled: { type: Number, default: 0 },
    numFeedbackSubmitted: { type: Number, default: 0 },
    numActionsSentD0: { type: Number, default: 0 },
    numLikesSentD0: { type: Number, default: 0 },
    numActionsSentBeforeFirstMatch: { type: Number },
    numLikesSentBeforeFirstMatch: { type: Number },

    gotFreeTrial: { type: Boolean },
    madePurchase: { type: Boolean }, // subscriptions only
    numPurchases: { type: Number, default: 0 }, // subscriptions only
    numRenewals: { type: Number, default: 0 }, // subscriptions only
    numSubscriptionCancellations: { type: Number, default: 0 },
    daysOnPlatformBeforePurchase: { type: Number },
    numCoinPurchases: { type: Number, default: 0 }, // coins only - number of transactions
    numCoinsPurchased: { type: Number, default: 0 }, // coins only - number of coins purchased
    numStripePurchases: { type: Number, default: 0 },
    numRefunds: { type: Number, default: 0 },
    revenueRefunded: { type: Number, default: 0 },

    revenue: { type: Number, default: 0 }, // all revenue
    coinRevenue: { type: Number, default: 0 }, // coins only
    numStickerPackPurchases: { type: Number, default: 0 }, // sticker packs only
    stickerPackRevenue: { type: Number, default: 0 }, // sticker packs only
    numSuperLikePurchases: { type: Number, default: 0 }, // super likes only
    superLikeRevenue: { type: Number, default: 0 }, // super likes only
    numNeuronPurchases: { type: Number, default: 0 }, // super likes only
    neuronRevenue: { type: Number, default: 0 }, // neurons only
    numBoostPurchases: { type: Number, default: 0 }, // boosts only - number of transactions
    numBoostsPurchased: { type: Number, default: 0 }, // boosts only - number of boosts purchased
    boostRevenue: { type: Number, default: 0 }, //boost only
    stripeRevenue: { type: Number, default: 0 }, // stripe purchases only
    saleRevenue: { type: Number, default: 0 },
    numSalePurchases: { type: Number, default: 0 },
    numCoinAdsWatched: { type: Number, default: 0 },
    numCoinsEarnedFromAds: { type: Number, default: 0 },
    numViewLastSeenPurchased: { type: Number, default: 0 },
    purchasedPremiumFrom: { type: String },
    purchasedCoinsFrom: { type: String },
    revivalUsedAt: { type: Date },
    numRevivalUsed: { type: Number, default: 0 },
    numRewindUsed: { type: Number, default: 0 },
    numBoostUsed: { type: Number, default: 0 },
    numLiftOffUsed: { type: Number, default: 0 },

    lastSeen: { type: Date },
    lastSeenAndroid: { type: Date },
    lastSeenIos: { type: Date },
    lastSeenWeb: { type: Date },
    loggedOutAt: { type: Date },
    numFollowers: { type: Number, default: 0 },
    numFollowing: { type: Number, default: 0 },
    numFollowRequests: { type: Number, default: 0 },
    numFollowersUpdatedAt: { type: Date },
    numProfileViews: { type: Number, default: 0 },
    numPendingLikes: { type: Number, default: 0 },
    numPendingReports: { type: Number, default: 0 },
    numTotalReports: { type: Number, default: 0 },
    numAwardsReceived: { type: Number, default: 0 },
    numAwardsSent: { type: Number, default: 0 },
    numQuestions: { type: Number, default: 0 },
    numComments: { type: Number, default: 0 },
    numStories: { type: Number, default: 0 },
    numPostLikesReceived: { type: Number, default: 0 },
    numPostLikesSent: { type: Number, default: 0 },
    numPostUnlikesSent: { type: Number, default: 0 },
    numPostShares: { type: Number, default: 0 },
    numPostClicks: { type: Number, default: 0 },
    numSecondsReadingOnFeed: { type: Number, default: 0 },
    numSecondsReadingComments: { type: Number, default: 0 },
    receivedFreeTrialFromBackend: { type: Boolean },
    receivedFreeTrialFromBackendExpirationDate: { type: Date },
    promptedForAppRating: { type: Boolean },
    promptedForAppRatingDate: { type: Date },
    numTipEmailsSent: { type: Number, default: 0 },
    numFlashSaleEmailsSent: { type: Number },
    numFlashSales: { type: Number, default: 0 },
    retention: {
      type: [Boolean],
      default: defaultRetentionArray,
    },
    activeDates: [ { type: Date } ],
    karmaFromLikingPosts: { type: Number, default: 0 },
    karmaFromFirstToMessage: { type: Number, default: 0 },
    leftOnReadAt: { type: Date },
    leftOnReadNotifiedAt: { type: Date },
    profileViewNotifiedAt: { type: Date },
    birthdayRewardReceivedYear: { type: Number },
    numAdditionalSwipes: { type: Number, default: 0 },
    numAdditionalSwipesVerified: { type: Number, default: 0 },
    had30DayInactivity: { type: Number, default: 0 },
    resurrected: { type: Number, default: 0 },
    freeSwipeReceivedForQods: [ { type: mongoose.Schema.Types.ObjectId, ref: 'Question' } ],

    automatic_revival_local_profiles: { type: Number },
    numLocalSwipesSentBeforeRevival: { type: Number },
    numLocalLikesSentBeforeRevival: { type: Number },
    numLocalPassesSentBeforeRevival: { type: Number },
    daysOnPlatformBeforeRevival: { type: Number },
    automaticRevivalDate: { type: Date },
    receivedSecretAdmirerOnDay: [ { type: Number } ],

    //APP-420
    numActionsSentActiveNow: { type: Number},
    numActionsSentMutualInterests: { type: Number},
    numActionsSentNearby: { type: Number},
    numActionsSentCompatiblePersonality: { type: Number},
    numActionsSentNewSoul: { type: Number},
    numActionsSentTopSoul: { type: Number},
    numActionsSentNoTags: { type: Number},
    numLikesSentActiveNow: { type: Number},
    numLikesSentMutualInterests: { type: Number},
    numLikesSentNearby: { type: Number},
    numLikesSentCompatiblePersonality: { type: Number},
    numLikesSentNewSoul: { type: Number},
    numLikesSentTopSoul: { type: Number},
    numLikesSentNoTags: { type: Number},

    //APP-339
    numQuestionsPostedAnonymously: { type: Number},
    numCommentsPostedAnonymously: { type: Number},

    // APP_99
    numNotificationsDailyAI: { type: Number, default: 0 },
    numNotificationsDailyNonAI: { type: Number, default: 0 },
    numNotificationsDailyAIV2: { type: Number, default: 0 },

    //ai image
    numAIImageBatchesCreated: { type: Number, default: 0 },
    numAIGenImageUsed: { type: Number, default: 0 },

    // legacy
    numPassesReceivedCurrentDay: { type: Number, default: 0 },
    numLikesReceivedCurrentDay: { type: Number, default: 0 },
    numActionsCurrentDay: { type: Number, default: 0 },
    numLikesSentCurrentDay: { type: Number, default: 0 },
    numPostsBoosted: { type: Number, default: 0 },

    lastNewJoinNotifiedAt: { type: Date },
    displayedCarrotProfilesWithMinDistance: { type: Boolean },
    displayedCarrotProfiles: { type: Boolean },
    carrotProfilesSeenByCarrotAlgoV2: { type: Number },
    carrotProfilesSeenByCarrotAlgoV1: { type: Number },
    currentExclusionListSize: { type: Number },

    numComplimentsSent: { type: Number, default: 0 },
    numComplimentsReceived: { type: Number, default: 0 },
    // app 876 v2
    numDaysRecycledProfilesUsedInForYou: { type: Number, default: 0 },
    numDaysRecycledProfilesUsedInForYouFound: { type: Number, default: 0 },
    numActionsReceivedDuringBoostQuota: { type: Number, default: 0 },
    numMeetUpChats: { type: Number, default: 0 },
    numContactExchangeChats: { type: Number, default: 0 },
    numMatchesAnalyzed: { type: Number, default: 0 },
    numYourTurnChats: { type: Number, default: 0 },

    //app 827
    socialProofCountryCode: { type: String},
    socialProofRank: { type: Number }
  },
  currentDayMetrics: {
    numProfileViews: { type: Number, default: 0 },
    numReferralsMade: { type: Number, default: 0 },
    numActionsSent: { type: Number, default: 0 },
    numActionsReceived: { type: Number, default: 0 },
    numLikesSent: { type: Number, default: 0 },
    numPassesSent: { type: Number, default: 0 },
    numLocalLikesSent: { type: Number, default: 0 },
    numLocalPassesSent: { type: Number, default: 0 },
    numGlobalLikesSent: { type: Number, default: 0 },
    numGlobalPassesSent: { type: Number, default: 0 },
    numApprovalsSent: { type: Number, default: 0 },
    numRejectionsSent: { type: Number, default: 0 },
    numUnmatchesSent: { type: Number, default: 0 },
    numBlocksSent: { type: Number, default: 0 },
    numMessagesSent: { type: Number, default: 0 },
    numLikesReceived: { type: Number, default: 0 },
    numPassesReceived: { type: Number, default: 0 },
    numApprovalsReceived: { type: Number, default: 0 },
    numRejectionsReceived: { type: Number, default: 0 },
    numUnmatchesReceived: { type: Number, default: 0 },
    numBlocksReceived: { type: Number, default: 0 },
    numMessagesReceived: { type: Number, default: 0 },
    numNotifications: { type: Number, default: 0 },
    numNotificationErrors: { type: Number, default: 0 },
    numMatches: { type: Number, default: 0 },
    numPurchases: { type: Number, default: 0 },
    revenue: { type: Number, default: 0 },
    numSessions: { type: Number, default: 0 },
    numEngagedSessions: { type: Number, default: 0 },
    numSecondsEngagementTime: { type: Number, default: 0 },
    avgSecondsEngagementTime: { type: Number, default: 0 },
    numPostsBoosted: { type: Number, default: 0 },
    numAdditionalSwipes: { type: Number, default: 0 },
    karmaFromFirstToMessage: { type: Number, default: 0 },
    karmaFromLikingPosts: { type: Number, default: 0 },
    karmaFromLikingPostsPartners: { type: [ { type: String, ref: 'User' } ], default: [] },
    karmaFromPostingStories: { type: Number, default: 0 },
    karmaFromPostingQuestions: { type: Number, default: 0 },
    coinsFromSharingPosts: { type: Number, default: 0 },
    topPicks: { type: [{ type: String, ref: 'User' }], default: [] },
    topPicksSimilarInterests: { type: [{ type: String, ref: 'User' }], default: [] },
    topPicksSameDatingGoals: { type: [{ type: String, ref: 'User' }], default: [] },
    topPicksSuperLiked: { type: [{ type: String, ref: 'User' }], default: [] },
    hitSwipeLimit: { type: Boolean, default: false },
    cachedUsersWhoLiked: { type: [{ type: String, ref: 'User' }], default: [] },
    cachedUsersWhoLikedSaved: { type: Boolean, default: false },
    freeSwipeFromQodReceived: { type: Boolean, default: false },
    showSecretAdmirerOnSwipe: { type: Number, default: null },
    swipes: { type: [{ type: String, ref: 'User' }], default: [] },
    pendingSwipes: { type: [{ type: String, ref: 'User' }], default: [] },
    numDaysRecycledProfilesUsedInForYou: { type: Number, default: 0 },
    numDaysRecycledProfilesUsedInForYouFound: { type: Number, default: 0 },
  },
  perDayMetrics: {
    numLikesSent: perDayMetricType,
    numLikesReceived: perDayMetricType,
    numActionsSent: perDayMetricType,
    numActionsReceived: perDayMetricType,
    numMessagesSent: perDayMetricType,
    numMatches: perDayMetricType,
  },
  firstUsageMetrics: {
    open_power_popup_numActionsSent: { type: Number },
    open_power_popup_numLikesSent: { type: Number },
    open_power_popup_numPassesSent: { type: Number },
    use_time_travel_numActionsSent: { type: Number },
    use_time_travel_numLikesSent: { type: Number },
    use_time_travel_numPassesSent: { type: Number },
    open_boost_popup_numActionsSent: { type: Number },
    open_boost_popup_numLikesSent: { type: Number },
    open_boost_popup_numPassesSent: { type: Number },
    use_boost_numActionsSent: { type: Number },
    use_boost_numLikesSent: { type: Number },
    use_boost_numPassesSent: { type: Number },
    open_superlove_popup_numActionsSent: { type: Number },
    open_superlove_popup_numLikesSent: { type: Number },
    open_superlove_popup_numPassesSent: { type: Number },
    use_superlove_numActionsSent: { type: Number },
    use_superlove_numLikesSent: { type: Number },
    use_superlove_numPassesSent: { type: Number },
    open_filter_popup_numActionsSent: { type: Number },
    open_filter_popup_numLikesSent: { type: Number },
    open_filter_popup_numPassesSent: { type: Number },
    use_filter_numActionsSent: { type: Number },
    use_filter_numLikesSent: { type: Number },
    use_filter_numPassesSent: { type: Number },
    open_dm_popup_numActionsSent: { type: Number },
    open_dm_popup_numLikesSent: { type: Number },
    open_dm_popup_numPassesSent: { type: Number },
    use_dm_numActionsSent: { type: Number },
    use_dm_numLikesSent: { type: Number },
    use_dm_numPassesSent: { type: Number },
  },
  events: {
    finished_signup: { type: Number, default: 0 },
    enterDeleteAccountFlow: { type: Number, default: 0 },
    enterCoinsPage: { type: Number, default: 0 },
    enterSuperLovePage: { type: Number, default: 0 },
    enterSocialPage: { type: Number, default: 0 },
    enterNotificationsPage: { type: Number, default: 0 },
    referralLinkCreated: { type: Number, default: 0 },
    enterInvitePage: { type: Number, default: 0 },
    sharePersonality: { type: Number, default: 0 },
    friendPosted: { type: Number, default: 0 },
    newUsersJoinedCity: { type: Number, default: 0 },
    fifteenSwipesRatingPromptShown: { type: Number, default: 0 },
    fifteenSwipesRatingPromptAccepted: { type: Number, default: 0 },
    tap_dimension_icon: { type: Number, default: 0 },
    change_dimension: { type: Number, default: 0 },
    flash_sale_after_inactive: { type: Number, default: 0 },
    recurring_monthly_sale: { type: Number, default: 0 },
    notify_flash_sale_expiration: { type: Number, default: 0 },
    numSessionEndNotifications: { type: Number, default: 0 },
    numNotificationsCompleteProfile: { type: Number, default: 0 },
    numNotificationsUploadPhoto: { type: Number, default: 0 },
    numLoveRefillNotifications: { type: Number, default: 0 },
    viewed_faq: { type: Number, default: 0 },
    open_boo_ai: { type: Number, default: 0 },
    viewed_perks: { type: Number, default: 0 },
    showDetailedReviewPrompt: { type: Number, default: 0 },
    tapped_super_love: { type: Number },
    inactive_day_2_push: { type: Number },
    inactive_day_4_push: { type: Number },
    signup_app_name: { type: Number },
    signup_app_gender: { type: Number },
    signup_app_birthday: { type: Number },
    signup_app_onboarding_video: { type: Number },
    signup_app_value_carousel: { type: Number },
    signup_app_value_planet: { type: Number },
    signup_app_value_compatible: { type: Number },
    signup_app_value_understand: { type: Number },
    signup_app_value_save: { type: Number },
    signup_app_value_friends: { type: Number },
    signup_app_value_universe: { type: Number },
    signup_app_value_interests: { type: Number },
    signup_app_value_love: { type: Number },
    signup_app_value_verified: { type: Number },
    signup_app_value_coins: { type: Number },
    signup_app_value_privacy: { type: Number },
    signup_app_value_booAi: { type: Number },
    signup_app_permissions: { type: Number },
    signup_app_quiz_intro: { type: Number },
    signup_app_short_quiz: { type: Number },
    signup_app_accurate_quiz: { type: Number },
    signup_app_input_personality: { type: Number },
    signup_app_personality_reveal: { type: Number },
    signup_app_recommended_personalities: { type: Number },
    signup_app_interests: { type: Number },
    signup_app_lifestyle: { type: Number },
    signup_app_work: { type: Number },
    signup_app_education: { type: Number },
    signup_app_block_contacts: { type: Number },
    signup_app_profile_prompts: { type: Number },
    signup_app_dimensions: { type: Number },
    signup_app_pictures: { type: Number },
    signup_app_verification: { type: Number },
    signup_app_description: { type: Number },
    signup_app_sexuality: { type: Number },
    signup_app_looking_for: { type: Number },
    signup_app_preferences: { type: Number },
    signup_app_relationship_status: { type: Number },
    signup_app_dating_sub_preferences: { type: Number },
    signup_app_added_dating_sub_preferences: { type: Number },
    signup_web_basic_information: { type: Number },
    signup_web_preferences: { type: Number },
    signup_web_value_carousel: { type: Number },
    signup_web_quiz_intro: { type: Number },
    signup_web_short_quiz: { type: Number },
    signup_web_accurate_quiz: { type: Number },
    signup_web_input_personality: { type: Number },
    signup_web_personality_reveal: { type: Number },
    signup_web_recommended_personalities: { type: Number },
    signup_web_profile_prompts: { type: Number },
    signup_web_interests: { type: Number },
    signup_web_dimensions: { type: Number },
    signup_web_pictures: { type: Number },
    signup_web_verification: { type: Number },
    signup_web_description: { type: Number },
    exitFlashSale: { type: Number },
    open_purchase_unlimitedLikes: { type: Number },
    open_purchase_unlimitedDMs: { type: Number },
    open_purchase_superLove: { type: Number },
    open_purchase_spiritRealm: { type: Number },
    open_purchase_seeWhoViewed: { type: Number },
    open_purchase_readReceipts: { type: Number },
    open_purchase_countryFilter: { type: Number },
    open_purchase_teleport: { type: Number },
    open_purchase_timeTravel: { type: Number },
    open_purchase_seeWhoLikesYou: { type: Number },
    open_purchase_ownProfile: { type: Number },
    open_purchase_renewal: { type: Number },
    open_purchase_flashSale: { type: Number },
    open_purchase_show_flash_sale_on_every_open: { type: Number },
    open_purchase_secondAppOpen: { type: Number },
    open_purchase_topPicks: { type: Number },
    open_power_popup: { type: Number },
    open_boost_popup: { type: Number },
    open_superlove_popup: { type: Number },
    open_dm_popup: { type: Number },
    finished_daily_boos: { type: Number },
    open_filter_popup : { type: Number },
    use_time_travel : { type: Number },
    use_boost : { type: Number },
    use_superlove : { type: Number },
    use_filter : { type: Number },
    use_dm : { type: Number },
    added_interest_anime : { type: Number },
    added_interest_gaming : { type: Number },
    added_interest_kpop : { type: Number },
    added_interest_fitness : { type: Number },
    open_universes_tab: { type: Number },
    open_custom_feeds: { type: Number },
    load_yoti: { type: Number },
    load_yoti_webview_error: { type: Number },
    load_yoti_error: { type: Number },
    shown_boost_ad_between_swiping: { type: Number },
    activated_from_boost_ad_between_swiping: { type: Number },
    shown_infinity_ad_between_swiping: { type: Number },
    purchased_from_infinity_ad_between_swiping: { type: Number },
    purchased_from_infinity_ad_from_requests: { type: Number },
    purchased_from_infinity_ad_from_views: { type: Number },
    out_of_souls: { type: Number },
  },
  localStats: {
    numLocalUsers: { type: Number },
    numLocalUsersDAU: { type: Number },
    numLocalUsersTargetGender: { type: Number },
    numLocalUsersTargetGenderMatchable: { type: Number },
    numLocalUsersActiveTargetGenderMatchable: { type: Number },
    numLocalUsersActiveTargetGenderMatchableInPreferences: { type: Number },
  },
  purchases: [
    {
      _id: false,
      productType: { type: String },
      productId: { type: String },
      daysOnPlatformBeforePurchase: { type: Number },
      revenue: { type: Number },
    },
  ],
  security: {
    locationHistory: [
      {
        date: Date,
        location: mongoose.Schema.Types.Point,
        countryCode: String,
        country: String,
        state: String,
        city: String,
      },
    ],
    internationalLocationHistory: [
      {
        date: Date,
        location: mongoose.Schema.Types.Point,
        countryCode: String,
        country: String,
        state: String,
        city: String,
      },
    ],
    ipHistory: [
      {
        _id: false,
        date: Date,
        ip: String,
        city: String,
        region: String,
        country: String,
        countryCode: String,
        location: mongoose.Schema.Types.Point,
        metro: Number,
        area: Number,
        timezone: String,
      },
    ],
    internationalIpHistory: [
      {
        _id: false,
        date: Date,
        ip: String,
        city: String,
        region: String,
        country: String,
        countryCode: String,
        location: mongoose.Schema.Types.Point,
        metro: Number,
        area: Number,
        timezone: String,
      },
    ],
  },
  termsAcceptedDate: { type: Date },
  termsAcceptedHistory: [ { type: Date } ],
  verificationTermsAcceptedDate: { type: Date },
  verification: {
    status: {
      type: String,
      enum: ['unverified', 'pending', 'verified', 'rejected', 'reverifying'],
      default: 'unverified',
    },
    updatedAt: { type: Date },
    pictures: [{ type: String }],
    verifiedBy: { type: String, ref: 'User' },
    verifiedDate: { type: Date },
    rejectionReason: { type: String, enum: rejectionReasons },
    assignedToQueueAt: { type: Date },
    pictureUploadedAt: { type: Date },
    newReverificationSystem: { type: Boolean },
    faceComparisonReferenceImage: { type: String },
    faceComparisonResults: [
      {
        image: { type: String },
        result: { type: String },
      }
    ],
    method: { type: String },
    attemptedVerificationDuringSignup: { type: Boolean },
    loadedYotiAt: { type: Date },
    numSecondsToCompleteYoti: { type: Number },
    reVerification: { type: Boolean }, // This is to track if the user was verified and now in the re-verification flow
    manualReview: {
      review1: {
        queue: { type: Number },
        reviewedBy: { type: String, ref: 'User' },
        reviewedAt: { type: Date },
        decision: { type: String },
        rejectionReason: { type: String },
        bannedReason: { type: String },
        bannedNotes: { type: String },
      },
      review2: {
        queue: { type: Number },
        reviewedBy: { type: String, ref: 'User' },
        reviewedAt: { type: Date },
        decision: { type: String },
        rejectionReason: { type: String },
        bannedReason: { type: String },
        bannedNotes: { type: String },
      },
      reviewFinal: {
        reviewedBy: { type: String },
        reviewedAt: { type: Date },
        decision: { type: String },
        rejectionReason: { type: String },
        bannedReason: { type: String },
        bannedNotes: { type: String },
      },
      firstDecision: { type: String },
      finalDecision: { type: String },
    },
  },
  verificationHistory: [
    {
      _id: false,
      status: { type: String, enum: ['unverified', 'pending', 'verified', 'rejected', 'reverifying'] },
      by: { type: String, ref: 'User' },
      date: { type: Date },
      reason: { type: String },
      manualReview: { type: mongoose.Mixed },
    },
  ],
  config: {
    test_config: { type: Boolean },
    atlas_search: { type: Boolean },
    use_boost_algorithm_for_daily_profiles: { type: Boolean },
    use_pusher_ios: { type: Boolean },
    show_network_debug_button: { type: Boolean },
    use_memory_image: { type: Boolean },
    show_sort_nearby_option: { type: Boolean },
    limit_likes: { type: Boolean },
    limit_likes_2_days: { type: Boolean },
    show_spotify: { type: Boolean },
    boost_first_three_posts: { type: Boolean },
    infinity_price_v3: { type: Boolean },
    infinity_price_v4: { type: Boolean },
    infinity_price_v6: { type: Boolean },
    ios_infinity_price_v2: { type: Boolean },
    ios_infinity_price_v3: { type: Boolean },
    new_karma_system: { type: Boolean },
    default_user_to_device_language_feed: { type: Boolean },
    make_teleport_free: { type: Boolean },
    show_distance_filter: { type: Boolean },
    w1_show_best: { type: Boolean },
    show_onboarding_video_all_languages: { type: Boolean },
    onboarding_video_v2: { type: Boolean },
    onboarding_video_dating_v2: { type: Boolean },
    onboarding_video_friends_v2: { type: Boolean },
    onboarding_video_women_dating: { type: Boolean },
    onboarding_video_men_dating: { type: Boolean },
    use_tenor: { type: Boolean },
    azure_liveness: { type: Boolean },
    azure_liveness_ios: { type: Boolean },
    allow_pose_verification: { type: Boolean },
    use_storekit1: { type: Boolean },
    app_001: { type: Boolean },
    app_002: { type: Boolean },
    app_003: { type: Boolean },
    app_004: { type: Boolean },
    app_763: { type: Boolean }, // set to true to enable nose challenge, false to enable yoti
    app_823: { type: Boolean }, // set to true to allow screenshots on app
    app_511: { type: Boolean },
    app_876_v3: { type: Boolean },
    app_892: { type: Boolean },
    app_882: { type: Boolean },
    app_870: { type: Boolean },
    app_229: { type: Boolean },
    app_829: { type: Boolean },
    app_785: { type: Boolean },
    app_888: { type: Boolean },
    app_914: { type: Boolean },
    app_920: { type: Boolean },
    app_886: { type: Boolean },
    app_887: { type: Boolean },
    app_894: { type: Boolean },
  },
  email: { type: String },
  emailDomain: { type: String },
  phoneNumber: { type: String },
  hiddenContacts: { type: Boolean },
  hideFromKeywords: [{ type: String }],
  hideFromNearby: { type: Boolean },
  pictures: [{ type: String }],
  aiGenPictures: [{ type: String }],
  originalPictures: { type: [{ type: String }], default: undefined },
  hiddenPictures: { type: [{ type: String }], default: undefined },
  hiddenProfileText: { type: [{ type: String }], default: undefined },
  privatePictures: { type: [{ type: String }], default: undefined },
  noFacePictures: [{ type: String }],
  convertedVideos: [{ type: String }],
  disabled: { type: Boolean },
  banned: { type: Boolean, default: false },
  shadowBanned: { type: Boolean, default: false },
  bannedReason: { type: String },
  bannedReasons: { type: [{ type: String }], default: undefined },
  bannedBy: { type: String, ref: 'User' },
  bannedNotes: { type: String },
  banNotice: {
    type: {
      _id: false,
      reason: { type: String },
      appealStatus: { type: String, enum: [ 'allowed', 'pending', 'rejected' ] },
    },
    default: undefined,
  },
  tempBanReason: { type: String },
  tempBanEndAt: { type: Date },
  tempBanBy: { type: String, ref: 'User' },
  profileTempBanReason: { type: String },
  profileTempBanReportId: { type: mongoose.Schema.Types.ObjectId, ref: 'Report' },
  profileTempBanInfringingText: { type: [{ type: String }], default: undefined },
  profileTempBanInfringingPictures: { type: [{ type: String }], default: undefined },
  appealedProfileTempBanInfringingText: { type: [{ type: String }], default: undefined },
  appealedProfileTempBanInfringingPictures: { type: [{ type: String }], default: undefined },
  bannedDate: { type: Date },
  banHistory: [
    {
      _id: false,
      action: { type: String, enum: [
        'ban', 'unban', 'tempBan', 'undoTempBan', 'notes',
        'appealSubmitted', 'appealApproved', 'appealRejected', 'appealDismissed',
        'profileTempBanAppealSubmitted', 'profileTempBanAppealApproved', 'profileTempBanAppealRejected', 'ProfileTempBanAppealDismissed',
        'tempBanAppealSubmitted', 'tempBanAppealApproved', 'tempBanAppealRejected', 'tempBanAppealDismissed',
      ] },
      by: { type: String, ref: 'User' },
      date: { type: Date },
      reason: { type: String },
      notes: { type: String },
      evidence: Object,
    },
  ],
  livenessVerification: {
    type: {
      id: { type: String },
      date: { type: Date, default: () => new Date() },
      imageWidth: { type: Number },
      imageHeight: { type: Number },
      areaLeft: { type: Number },
      areaTop: { type: Number },
      areaWidth: { type: Number },
      areaHeight: { type: Number },
      minFaceAreaPercent: { type: Number },
      noseLeft: { type: Number },
      noseTop: { type: Number },
      noseWidth: { type: Number },
      noseHeight: { type: Number },
      frames: [
        {
          _id: false,
          timestamp: { type: Date, default: () => new Date() },
          key: { type: String },
        }
      ],
      livenessSuccess: { type: Boolean },
      livenessFailureReason: { type: String },
      matchingPictures: [ { type: String } ],
      notMatchingPictures: [ { type: String } ],
      compareFacesSuccess: { type: Boolean },
      rejectionReason: { type: String },
      manuallyCheckedBy: { type: String, ref: 'User' },
      manuallyCheckedDate: { type: Date },
      manuallyCheckedResult: { type: Boolean },
      manuallyCheckedRejectionReason: { type: String },
    },
    default: undefined,
  },
  fcmToken: { type: String, maxlength: 4096 },
  fcmTokenUpdatedAt: { type: Date },
  fcmTokenFirstUpdatedOnDay: { type: Number },
  deletionRequestDate: { type: Date },
  accountDeletionGracePeriod: { type: Number },
  disableLocationUpdates: { type: Boolean },
  originalLocation: {
    countryCode: { type: String },
    country: { type: String },
    state: { type: String },
    city: { type: String },
  },
  locationOverride: {
    countryCode: { type: String },
    country: { type: String },
    state: { type: String },
    city: { type: String },
  },
  hideCity: { type: Boolean },
  hideReadReceipts: { type: Boolean },
  showMyInfinityStatus : { type: Boolean, default: true },
  premiumExpiration: { type: Date },
  premiumV2Expiration: { type: Date },
  godModeExpiration: { type: Date },
  godModeMonthlySuperLikeAnniversary: { type: Date },
  infinityWeeklySuperLikeAnniversary: { type: Date },
  freeWeeklySuperLikeAnniversary: { type: Date },
  productIdPurchased: { type: String },
  unlimitedLikesExpiration: { type: Date },
  unlimitedDmsExpiration: { type: Date },
  boostExpiration: { type: Date },
  boostActivityToken: { type: String },
  boostDurationMinutes: { type: Number },
  telepathyPreviewMbti: { type: String },
  interplanetaryMode: { type: Boolean, default: true },
  savedPromoCode: { type: String },
  appVersion: { type: String, trim: true, maxlength: 50 },
  signupSource: { type: String, enum: validOs },
  webSignupCategory: { type: String },
  webSignupPage: { type: String },
  webFirstVisitCategory: { type: String },
  webFirstVisitPage: { type: String },
  webFirstVisitReferringDomain: { type: String },
  webFirstVisitReferringURL: { type: String },
  webDeviceId: { type: String },
  appDeviceId: { type: String },
  os: { type: String, enum: validOs },
  osVersion: { type: String, trim: true, maxlength: 50 },
  phoneModel: { type: String, trim: true, maxlength: 500 },
  deviceSize: { type: String, enum: ['s','m','l', null], default: null},
  signupMethod: { type: String },
  deviceId: { type: String, trim: true, maxlength: 500 },
  deviceIdHistory: [ { type: String } ],
  isPhysicalDevice: { type: Boolean },
  jailbroken: { type: Boolean },
  passedAppCheck: { type: Boolean },
  failedAppCheck: { type: Boolean },
  appCheckErrorCode: { type: String },
  deviceLanguage: { type: String, enum: languageCodes },
  timezone: { type: String, trim: true, maxlength: 500 },
  ipData: {
    _id: false,
    date: Date,
    ip: String,
    city: String,
    region: String,
    country: String,
    countryCode: String,
    location: mongoose.Schema.Types.Point,
    metro: Number,
    area: Number,
    timezone: String,
    userAgent: String
  },
  utm_source: { type: String },
  utm_medium: { type: String },
  utm_campaign: { type: String },
  utm_content: { type: String },
  adset_name: { type: String },
  advertisingId: { type: String },
  optOutOfAdTargeting: { type: Boolean },
  autoplay: { type: Boolean, default: true },
  admin: { type: Boolean },
  adminAttemptedAccountDeletion: { type: Boolean },
  adminPermissions: {
    type: {
      _id: false,
      all: { type: Boolean },
      support: { type: Boolean },
      approveQod: { type: Boolean },
      approveInterest: { type: Boolean },
      approveDatabase: { type: Boolean },
      translator: { type: String, enum: languageCodes },
      translatorManager: { type: Boolean },
      setConfig: { type: Boolean },
      manager: { type: Boolean },
      zendesk: { type: Boolean },
      moderation: { type: Boolean },
      payments: { type: Boolean },
      reviewBanAppeal: { type: Boolean },
      dualQueueTiebreaker: { type: Boolean },
    },
    default: undefined,
  },
  stripeCustomerId: { type: String },
  stripePaymentCountry: { type: String },
  stripeCurrency: { type: String },
  stripeCurrencyBackfilled: { type: Boolean },
  translator: { type: Boolean },
  verified: { type: Boolean },
  cannotBeBanned: { type: Boolean },
  premiumFlashSaleOneTime: { type: Boolean },
  premiumFlashSaleEndDate: { type: Date },
  premiumFlashSaleReason: { type: String },
  superLikeFlashSaleEndDate: { type: Date },
  coinsFlashSaleEndDate: { type: Date },
  boostsFlashSaleEndDate: { type: Date },
  locale: { type: String },
  countryLocale: { type: String },
  socialFeedLanguage: { type: String },
  interests: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Interest' }],
  interestNames: [{ type: String }],
  interestPoints: [{
    _id: false,
    interest: { type: String },
    language: { type: String },
    points: { type: Number },
    rank: { type: Number },
  }],
  hiddenInterests: [{ type: String }],
  instantMatchEnabled: { type: Boolean },
  instantMatchPartner: { type: String, ref: 'User' },
  instantMatchResetTime: { type: Date, default: () => new Date() },

  numSuperLikes: { type: Number, default: 0 },
  numSuperLikesFree: { type: Number, default: 0 },
  freeSuperLikeReceivedDate: { type: Date },
  numDbUploads: { type: Number, default: 0 },
  dbUploadCoinsReceived: { type: Number, default: 0 },
  dbUploadKarmaReceived: { type: Number, default: 0 },
  karma: { type: Number, default: 0 },
  lastSentKarma: { type: Number },
  karmaTier: { type: Number, default: 0 },
  newKarmaTier: { type: Number, default: 0 },
  numBooAINeurons: { type: Number, default: 3 },
  numBoosts: { type: Number, default: 0 },
  lastSentNumLikesReceived: { type: Number },
  lastSentNumPendingLikes: { type: Number },
  postBoostPopupHandled:{ type: Boolean },

  approveAllFollowers: { type: Boolean, default: true },
  autoFollowLikes: { type: Boolean, default: true },
  autoFollowMatches: { type: Boolean, default: true },
  languages: [{
    type: String,
    enum: languageCodes,
  }],
  ethnicities: {
    type: [{ type: String, enum: ethnicities }],
    default: undefined,
  },
  awards: {
    type: Map,
    of: Number,
  },
  tenjin: {
    _id: false,
    adNetwork: { type: String },
    campaignId: { type: String },
    campaignName: { type: String },
    clickId: { type: String },
    creativeName: { type: String },
    tenjinCampaignId: { type: String },
  },
  kochava: { type: mongoose.Mixed },
  appsflyer: {
    status: { type: String },
    appsflyer_id: { type: String, default: undefined },
    payload: { type: mongoose.Mixed }
  },
  appsflyerReceivedAt: { type: Date },
  howDidYouHearAboutUs: { type: String },
  sexuality: { type: String, enum: sexualityChoices },
  sexualityVisibility: { type: String, enum: ['all', 'lgbtq', 'sameSexuality', 'invisible'] },
  sexualityPopupClosed: { type: Boolean },
  audioDescriptionTranscription: { type: String, trim: true, default: undefined },

  //app 375
  lastNotificationTopicsSent: {
    type: Map,
    of: Date,
    default: {}
  },

  recordingAllocationEvaluated: { type: Boolean },
  partnerCampaign: { type: String, default: undefined },
  partnerCampaignSubniche: { type: String },
  minDistanceForCarrotAlgo : { type: Number }, //in miles
  translationRatings: {
    ja: { type: Number },
    ko: { type: Number },
    th: { type: Number },
    vi: { type: Number },
    ms: { type: Number },
    ar: { type: Number },
  },
  signupAppVersion: { type: String },
  lastExclusionRecalculated: { type: Date },
  backfillChatPerUserStateBegan: { type: Date },
  backfillChatPerUserStateComplete: { type: Boolean },
  backfillNumYourTurnChatsBegan: { type: Date },
  backfillNumYourTurnChatsComplete: { type: Boolean },
  // Track if bot messages have been sent to the user
  booMessages: {
    welcomeMessage: { type: Boolean }, // If true, the welcome message has been sent and will not be sent again
    dayOneTrigger: { type: Boolean }, // If true, the day one message has been sent and will not be sent again
    infinitySaleTriggerDate: { type: Date }, // Date of the last infinity sale message sent
    superLikeSaleTriggerDate: { type: Date }, // Date of the last super like sale message sent
    coinsSaleTriggerDate: { type: Date }, // Date of the last coins sale message sent
    boostsSaleTriggerDate: { type: Date }, // Date of the last boosts sale message
    dayThreeTrigger: { type: Boolean }, // If true, the day three message has been sent
    notificationTrigger: { type: Boolean }, // If true, the notification message has been sent or ignored(becuase user enabled notifications before message was sent), will not be sent again
    notificationTriggerInit: { type: Date }, // Marking the time when user received a message and notification is disabled
    messageSender: { type: String }, // The name of the user who sent the message
    whatsNewSentAtVersion: { type: String }, // App version for which message was sent
    // notificationTriggerInit, messageSender are intermitent variables, once notificationTrigger logic is processed they will be undefined
  },
  accountRestrictions: {
    type: {
      _id: false,
      tempBan: {
        type: {
          _id: false,
          appealStatus: { type: String, enum: [ 'allowed', 'pending', 'rejected' ] },
          evidence: Object
        },
        default: undefined,
      },
      profileTempBan: {
        type: {
          _id: false,
          appealStatus: { type: String, enum: [ 'allowed', 'pending', 'rejected' ] },
        },
        default: undefined,
      }
    },
    default: undefined,
  },
  alreadyPurchasedPremiumOnDeviceId: { type: String },
  alreadyPurchasedPremiumOnUserId: { type: String },
  aiFilterPreference: { type: String, ref: 'AiFilter', default: undefined },
  aiFilter: { type: String, default: undefined },
  fetchNewProfileAnalysis: { type: Boolean },
  //app_339
  anonymousProfileNickname: {
    type: String,
    trim: true,
    maxlength: 50,
  },
  hideFromVisionSearch: { type: Boolean },
  booInfinityCopy: { type: String, default: undefined }, // app_797
  changedBirthday: { type: Boolean, default: undefined }, // app_809
  hideUnverifiedUsers: { type: Boolean, default: true },
  tempBanReviewPendingUntil: { type: Date, default: undefined },
}, {
  versionKey: false,
  minimize: false,
});

// Indexes
// =============================================================================


userSchema.index({
  location: '2dsphere',
});

userSchema.index(
  {
    'metrics.numQuestions': 1,
    age: 1,
    genderPreferenceHash: 1,
    countryCode: 1,
    'personality.mbti': 1,
    'preferences.minAge': 1,
    'preferences.maxAge': 1,
    enneagram: 1,
    horoscope: 1,
    'verification.status': 1,
  },
  { partialFilterExpression: { 'metrics.numQuestions': { $gt: 0 } } },
);

userSchema.index({
  deletionRequestDate: 1,
});

userSchema.index({
  deviceId: 1,
});

userSchema.index({
  advertisingId: 1,
});

userSchema.index({
  email: 1,
});

userSchema.index({
  phoneNumber: 1,
});

userSchema.index({
  fcmToken: 1,
});

userSchema.index({
  uid: 1,
});

userSchema.index({
  createdAt: 1,
});

userSchema.index({
  'ipData.ip': 1,
  createdAt: 1,
});

userSchema.index({
  premiumFlashSaleEndDate: 1,
});

userSchema.index({
  interestNames: 1,
  'metrics.lastSeen': -1,
});

userSchema.index({
  'verification.status': 1,
  'verification.updatedAt': 1,
});

userSchema.index(
  {
    'verification.status': 1,
    'verification.assignedToQueueAt': 1,
  },
  { partialFilterExpression: { 'verification.status': { $in: ['pending','reverifying'] } } },
);

userSchema.index(
  {
    'verification.manualReview.review1.queue': 1,
    'verification.updatedAt': 1,
  },
  { partialFilterExpression: { 'verification.manualReview.review1.decision': { $type: 'null' } } },   // exists *and* is null
);

userSchema.index(
  {
    'verification.manualReview.review2.queue': 1,
    'verification.updatedAt': 1,
  },
  { partialFilterExpression: { 'verification.manualReview.review2.decision': { $type: 'null' } } },   // exists *and* is null
);

userSchema.index(
  {
    'verification.updatedAt': 1,
  },
  { partialFilterExpression: { 'verification.manualReview.reviewFinal.decision': { $type: 'null' } } },   // exists *and* is null
);

userSchema.index({
  'verification.verifiedDate': 1,
});

userSchema.index({
  'metrics.lastSeen': 1,
});

userSchema.index({
  timezone: 1,
});

userSchema.index(
  { dbUploadKarmaReceived: 1 },
  { partialFilterExpression: { dbUploadKarmaReceived: { $gt: 0 } } },
);

userSchema.index(
  { stripeCustomerId: 1 },
  { partialFilterExpression: { stripeCustomerId: { $exists: true } } },
);

userSchema.index(
  { firstName: 1, _id: 1 },
);

userSchema.index(
  { anonymousProfileNickname: 1 },
  { partialFilterExpression: { anonymousProfileNickname: { $exists: true } } },
);

userSchema.index(
  { boostExpiration: 1 },
  { partialFilterExpression: { boostActivityToken: { $exists: true } } },
);

userSchema.index(
  {
    createdAt: 1,
    emailDomain: 1,
  },
  {
    partialFilterExpression: {
      shadowBanned: true,
      os: 'web',
      'verification.status': { $in: [ 'unverified', 'rejected' ] },
    }
  },
);

// Methods
// =============================================================================

// added to avoid circular dependency
userSchema.statics.setSendSocketEventForDeleteChat = function (fn) {
  sendSocketEventForDeleteChat = fn;
};
userSchema.statics.setReportLibFunctions = function (eligibleForVerificationFn, unbanFn, preemptiveModerationForUserProfileTextFn) {
  eligibleForVerification = eligibleForVerificationFn;
  unban = unbanFn;
  preemptiveModerationForUserProfileText = preemptiveModerationForUserProfileTextFn;
};
userSchema.statics.setSocialLibFunctions = function (updateInterestRanksFn) {
  updateInterestRanks = updateInterestRanksFn;
};

userSchema.methods.calculateViewableInDailyProfiles = function () {
  this.viewableInDailyProfiles = calculateViewableInDailyProfiles(this);
};

userSchema.methods.findCustomFeed = function (feedName) {
  if (!feedName) {
    return;
  }
  return this.customFeeds.find(feed => feed.feedName == feedName);
};

userSchema.methods.updateNumMinutesUntilParams = async function (fields) {
  const user = this;
  if (!Array.isArray(fields)) {
    fields = [fields];
  }
  fields = fields.filter(field => !(user && user.metrics && user.metrics[field]));
  if (!fields.length) {
    return;
  }
  const diffMs = new Date() - user.createdAt;
  const diffMinutes = Math.floor((diffMs / 1000) / 60);
  for (const field of fields) {
    user.metrics[field] = diffMinutes;
  }
  await user.save();
};

userSchema.methods.resetCurrentDayMetricsIfNeeded = async function () {
  if (!this.metrics.swipeLimitResetTime || this.metrics.swipeLimitResetTime <= Date.now()) {
    this.metrics.swipeLimitResetTime = moment().add(12, 'hours');
    this.metrics.numActionsCurrentDay = 0;
    this.metrics.numLikesSentCurrentDay = 0;
    this.metrics.numPassesReceivedCurrentDay = 0;
    this.metrics.numLikesReceivedCurrentDay = 0;
    this.currentDayMetrics.hitSwipeLimit = false;
    this.currentDayMetrics.swipes = [];
    this.currentDayMetrics.pendingSwipes = [];
    await this.save();
  }

  if (this.metrics.currentDayResetTime > Date.now()) {
    return;
  }

  this.metrics.currentDayResetTime = getCurrentDayResetTime(this.timezone);
  for (const [key, value] of Object.entries(userSchema.obj.currentDayMetrics)) {
    this.currentDayMetrics[key] = value.default;
  }

  await this.save();

  // This call runs in the background
  this.constructor.computeLocalStats(this._id);
};

userSchema.methods.activateInfinitySuperLikes = async function () {
  const user = this;
  if (!user.infinityWeeklySuperLikeAnniversary || user.infinityWeeklySuperLikeAnniversary < new Date()) {
    user.infinityWeeklySuperLikeAnniversary = DateTime.fromJSDate(new Date()).plus({ weeks: 1 }).toJSDate();
    const previousNumSuperLikesFree = user.numSuperLikesFree || 0;
    user.numSuperLikesFree = 2;
    if (user.numSuperLikesFree > previousNumSuperLikesFree) {
      await SuperLikeTransaction.create({
        user: user._id,
        freeSuperLoveTransactionAmount: user.numSuperLikesFree - previousNumSuperLikesFree,
        freeSuperLoveNewBalance: user.numSuperLikesFree,
        paidSuperLoveTransactionAmount: 0,
        paidSuperLoveNewBalance: user.numSuperLikes,
        description: 'received free super love',
      });
    }
  }
};

userSchema.methods.initConfig = function () {
  let key;

  // custom splits go here
  key = 'default_user_to_device_language_feed';
  if (this.config[key] === undefined) {
    if (this.versionAtLeast('1.11.66') && this.deviceLanguage && !metricsLib.getPopularLanguages().includes(this.deviceLanguage)) {
      this.config[key] = basic.assignConfig(0.5);
    }
  }

  // version-dependent configs
  /*
  key = 'show_spotify';
  if (this.config[key] === undefined) {
    if (this.versionAtLeast('1.11.49')) {
      this.config[key] = basic.assignConfig(0.5);
    }
  }
  */

  /*
  key = 'make_teleport_free';
  if (this.config[key] === undefined) {
    if (this.versionAtLeast('1.11.70')) {
      this.config[key] = basic.assignConfig(0.5);
    }
  }
  */

  /*
  key = 'azure_liveness_ios';
  if (this.config[key] === undefined) {
    if (this.versionAtLeast('1.13.47') && this.locale == 'en' && this.os == 'ios') {
      this.config[key] = basic.assignConfig(0.5);
    }
  }
  */

  key = 'app_001';
  if (this.config[key] === undefined) {
    if (process.env.NODE_ENV === 'test') {
      this.config[key] = true;
    }
  }

  key = 'app_002';
  if (this.config[key] === undefined) {
    if (process.env.NODE_ENV === 'test') {
      this.config[key] = true;
    }
  }

  key = 'app_003';
  if (this.config[key] === undefined) {
    if (process.env.NODE_ENV === 'test') {
      this.config[key] = true;
    }
  }

  key = 'app_004';
  if (this.config[key] === undefined) {
    if (process.env.NODE_ENV === 'test') {
      this.config[key] = true;
    }
  }

  key = 'app_876_v3';
  if (this.config[key] === undefined) {
    if (this.versionAtLeast('1.13.95')) {
      this.config[key] = basic.assignConfig(0.5);
    }
  }

  key = 'app_892';
  if (this.config[key] === undefined) {
    if (this.versionAtLeast('1.13.97')) {
      this.config[key] = basic.assignConfig(0.5);
    }
  }

  key = 'app_882';
  if (this.config[key] === undefined) {
    if (this.versionAtLeast('1.13.97')) {
      this.config[key] = basic.assignConfig(0.5);
    }
  }

  key = 'app_870';
  if (this.config[key] === undefined) {
    if (this.versionAtLeast('1.13.97')) {
      this.config[key] = basic.assignConfig(0.5);
    }
  }

  key = 'app_829';
  if (this.config[key] === undefined) {
    if (this.versionAtLeast('1.13.97')) {
      this.config[key] = basic.assignConfig(0.5);
    }
  }

  key = 'app_888';
  if (this.config[key] === undefined) {
    if (this.versionAtLeast('1.13.99')) {
      this.config[key] = basic.assignConfig(0.5);
    }
  }

  key = 'app_914';
  if (this.config[key] === undefined) {
    if (this.versionAtLeast('1.13.99')) {
      this.config[key] = basic.assignConfig(0.5);
    }
  }

  key = 'app_920';
  if (this.config[key] === undefined) {
    if (this.versionAtLeast('1.13.99')) {
      this.config[key] = basic.assignConfig(0.5);
    }
  }

  key = 'app_886';
  if (this.config[key] === undefined) {
    if (this.versionAtLeast('1.13.99')) {
      this.config[key] = basic.assignConfig(0.5);
    }
  }

  // disabled until related metrics are ready
  /*
  key = 'app_887';
  if (this.config[key] === undefined) {
    if (this.versionAtLeast('1.13.99')) {
      this.config[key] = basic.assignConfig(0.5);
    }
  }

  key = 'app_894';
  if (this.config[key] === undefined) {
    if (this.versionAtLeast('1.13.99')) {
      this.config[key] = basic.assignConfig(0.5);
    }
  }
  */

  /*
  key = 'app_229';
  if (this.config[key] === undefined) {
    if (this.versionAtLeast('1.13.99')) {
      this.config[key] = basic.assignConfig(0.5);
    }
  }
  */

  /*
  key = 'app_785';
  if (this.config[key] === undefined) {
    if (this.versionAtLeast('1.13.99')) {
      this.config[key] = basic.assignConfig(0.5);
    }
  }
  */

  const { niche, subniche } = getNicheFromCampaign(this);
  if (niche) {
    this.partnerCampaign = niche;
  }
  if (subniche) {
    this.partnerCampaignSubniche = subniche;
  }

  onboardingVideoConfigLib.assignOnboardingVideoExperimentConfig(this);

  // generic 50% split for other configs
  const configs = [
  ];
  for (const key of configs) {
    if (this.config[key] === undefined) {
      this.config[key] = basic.assignConfig(0.5);
    }
  }
};

userSchema.methods.setLocation = function (location, countryCode) {

  function round(value, step) {
      step || (step = 1.0);
      var inv = 1.0 / step;
      return Math.round(value * inv) / inv;
  }

  this.location = location;
  this.longitude = location ? round(location.coordinates[0], 0.1) : undefined;
  this.latitude = location ? round(location.coordinates[1], 0.1) : undefined;
  this.longitude2 = location ? round(location.coordinates[0], 0.02) : undefined;
  this.latitude2 = location ? round(location.coordinates[1], 0.02) : undefined;
  this.longitude3 = location ? round(location.coordinates[0], 2.5) : undefined;
  this.latitude3 = location ? round(location.coordinates[1], 2.5) : undefined;

  this.countryCode = countryCode;
  this.countryGroup = countryLib.getCountryGroup(countryCode);
  this.region = getRegion(countryCode);
};

userSchema.methods.initOriginalPictures = function () {
  if (!this.originalPictures) {
    this.originalPictures = this.pictures;
  }
};

userSchema.methods.setBirthday = function (birthday, timezone) {
  this.birthday = birthday;
  this.horoscope = getHoroscope(birthday);
  this.age = getAge(birthday, timezone);
};

userSchema.methods.setVerificationStatus = async function (status, reason, by) {
  /*
  if (this.shadowBanned && status == 'pending') {
    this.verification.status = 'rejected';
    this.verification.updatedAt = moment().add(1, 'day');
    this.verification.assignedToQueueAt = undefined;
    return;
  }
  */
  const user = this;
  const previousStatus = user.verification.status;
  const previousBanStatus = user.shadowBanned;

  user.verification.status = status;
  user.verification.updatedAt = Date.now();
  user.verification.assignedToQueueAt = undefined;
  user.calculateViewableInDailyProfiles();

  user.verificationHistory.push({
    date: new Date(),
    status,
    by,
    reason,
  });

  if (status == 'verified') {
    await user.updateNumMinutesUntilParams('numMinutesUntilVerified');
  }
  if (status == 'pending' || status == 'reverifying') {
    user.verification.manualReview = {
      review1: {
        queue: 1,
        decision: null,
      },
      review2: {
        queue: 2,
        decision: null,
      },
    }
  }
  if (status != 'rejected') {
    user.verification.rejectionReason = undefined;
  }

  if (eligibleForVerification && unban) {
    if (status == 'verified' && user.shadowBanned && eligibleForVerification(user.bannedReason)) {
      await unban(user, null, 'passed verification');
    }
  }

  if (status == 'pending') {
    const params = {
      MetricData: [
        {
          MetricName: 'IncomingPhotoVerification',
          Value: 1,
          Unit: 'Count',
        },
      ],
      Namespace: `SupportTeamMetrics_${process.env.NODE_ENV}`,
    };
    await cloudwatch.putMetricData(params).promise();
  }
  else if (status == 'reverifying') {
    const params = {
      MetricData: [
        {
          MetricName: 'IncomingPhotoReverification',
          Value: 1,
          Unit: 'Count',
        },
      ],
      Namespace: `SupportTeamMetrics_${process.env.NODE_ENV}`,
    };
    await cloudwatch.putMetricData(params).promise();
  }

  const wasVerified = ['verified', 'reverifying'].includes(previousStatus);
  const isNowVerified = status === 'verified';
  let needsReranking = false;
  const query = { user: this._id };

  if (!wasVerified && isNowVerified) {
    // User just became verified → run preemptive moderation
    await preemptiveModerationForUserProfileText(user);

    await InterestPoint.updateMany({ user: this._id }, { isUnverified: false });
    // If user is banned during preemptive moderation, no need to re-rank again
    const newlyBanned = !previousBanStatus && user.shadowBanned;
    needsReranking = !newlyBanned;
  }

  if (wasVerified && !['verified', 'reverifying'].includes(status)) {
    await InterestPoint.updateMany({ user: this._id }, { isUnverified: true });
    needsReranking = true;
    query.rank = { $gt: 0 };
  }

  if (needsReranking) {
    const entries = await InterestPoint.find(query);

    for (const entry of entries) {
      await updateInterestRanks(entry.interest, entry.language);
    }
  }
};

userSchema.methods.getBoostDurationMinutes = function () {
  if (this.versionAtLeast('1.11.14')) {
    return 60;
  }
  return 30;
};

userSchema.methods.versionAtLeast = function (version) {
  if (!this.appVersion) {
    return false;
  }
  return cmp(this.appVersion, version) >= 0;
};

userSchema.methods.isConfigTrue = function (configName) {
  if (!this.config || !this.config[configName]) {
    return false;
  }
  return this.config[configName];
};

userSchema.methods.getDaysOnPlatform = function () {
  return Math.abs(moment().diff(this.createdAt, 'days'));
};

userSchema.methods.isSocialCoinRewardActive = function () {
  return false;
};

userSchema.methods.supportsGroupChat = function () {
  return this.versionAtLeast('1.10.58');
};

userSchema.methods.isVerified = function () {
  if (this.verification.status == 'verified'
   || this.verification.status == 'reverifying') {
    return true;
  }
  return false;
};

userSchema.methods.isBoostActive = function () {
  if (this.boostExpiration > new Date()) {
    return true;
  }
  return false;
};

userSchema.methods.isPostBoostPeriod = function () {
  if (this.boostExpiration && this.boostDurationMinutes
      && DateTime.fromJSDate(this.boostExpiration).plus({ minutes: this.boostDurationMinutes }).toJSDate() > new Date()) {
    return true;
  }
  return false;
};

userSchema.methods.activateFlashSale = function (reason, hours=6) {
  if(this.alreadyPurchasedPremiumOnUserId) return;
  this.premiumFlashSaleEndDate = DateTime.utc().plus({ hours }).toJSDate();
  this.premiumFlashSaleReason = reason;
  this.metrics.numFlashSales += 1;
};

userSchema.methods.promptAppRating = function () {
  const user = this;
  if (user.metrics.promptedForAppRatingDate > moment().subtract(30, 'days')) {
    return false;
  }
  if (user.metrics.numMessagesSent >= 5
    && user.metrics.numMessagesReceived >= 5) {
    return true;
  }
  if ((user.lastSentKarma || 0) < getKarmaTiers()[user.karmaTier]) {
    return true;
  }
  return false;
};

userSchema.methods.updateUserLastSeen = function () {
  const user = this;
  const date = new Date().setHours(0, 0, 0, 0);
  user.updatedAt = date;
  user.metrics.lastSeen = new Date();
  const day = moment().diff(user.createdAt, 'days');
  if (day >= 0 && day <= 90) {
    user.metrics.retention.set(day, true);
  }
  if (!user.metrics.activeDates.find(x => x.getTime() == date)) {
    user.metrics.activeDates.push(date);
  }
  if (user.metrics.had30DayInactivity) {
    user.metrics.resurrected = 1;
  }
};

userSchema.statics.findByUid = async function (uid) {
  let user = await this.findById(uid);
  if (!user || user.disabled) {
    const u = await this.findOne({ uid });
    if (u) {
      user = u;
    }
  }
  return user;
};

userSchema.statics.incrementMetrics = async function (userId, metrics) {
  const incrementParams = {};
  let day = -1
  if (metrics.some(metric => metric in userSchema.obj.perDayMetrics)) {
    const user = await this.findById(userId, 'createdAt');
    day = moment().diff(user.createdAt, 'days');
  }
  for (const metric of metrics) {
    if (metric in userSchema.obj.metrics) {
      incrementParams[`metrics.${metric}`] = 1;
    }
    if (metric in userSchema.obj.currentDayMetrics) {
      incrementParams[`currentDayMetrics.${metric}`] = 1;
    }
    if (day >= 0 && day <= 7 && metric in userSchema.obj.perDayMetrics) {
      incrementParams[`perDayMetrics.${metric}.${day}`] = 1;
    }
  }
  await this.updateOne(
    { _id: userId },
    { $inc: incrementParams },
  );
};

userSchema.statics.incrementMetric = async function (userId, metricName, increment) {
  await this.updateOne(
    { _id: userId },
    { $inc: { [`metrics.${metricName}`]: increment } },
  );

  if (increment < 0) {
    await this.updateOne(
      { _id: userId },
      { $max: { [`metrics.${metricName}`]: 0 } },
    );
  }
};

userSchema.statics.incrementNumFollowers = async function (userId) {
  await this.incrementMetric(userId, 'numFollowers', 1);
};
userSchema.statics.decrementNumFollowers = async function (userId) {
  await this.incrementMetric(userId, 'numFollowers', -1);
};

userSchema.statics.incrementNumFollowing = async function (userId) {
  await this.incrementMetric(userId, 'numFollowing', 1);
};
userSchema.statics.decrementNumFollowing = async function (userId) {
  await this.incrementMetric(userId, 'numFollowing', -1);
};

userSchema.statics.incrementNumFollowRequests = async function (userId) {
  await this.incrementMetric(userId, 'numFollowRequests', 1);
};
userSchema.statics.decrementNumFollowRequests = async function (userId) {
  await this.incrementMetric(userId, 'numFollowRequests', -1);
};

// Computing Statistics
// =============================================================================

userSchema.statics.computeLocalStats = async function (userId) {
  // temporarily disable this
  return;

  const maxDistance = 50;
  const user = await this.findById(userId);
  if (!user.location) {
    return;
  }
  let exclusionList = await ExclusionList.findOne({ user: user._id });
  exclusionList = exclusionList ? exclusionList.exclusionList : [];

  const baseFilter = {
    $and: [
      { 'pictures.0': { $exists: true } },
      { hidden: { $ne: true } },
      { banned: { $ne: true } },
      { shadowBanned: { $ne: true } },
      { location: { $exists: true } },
      { _id: { $nin: [user._id] } },
    ],
  };

  const excludeInactiveFilter = { 'scores.decayedScore': { $gt: 0 } };
  const dauFilter = {
    'metrics.lastSeen': { $gt: DateTime.utc().minus({ days: 1 }).toJSDate() },
  };
  const exclusionListFilter = { _id: { $nin: exclusionList } };
  const genderCompatibleFilter = {
    $or: [
      {
        $and: [
          { gender: { $in: user.preferences.dating } },
          { 'preferences.dating': user.gender },
        ],
      },
      {
        $and: [
          { gender: { $in: user.preferences.friends } },
          { 'preferences.friends': user.gender },
        ],
      },
      // backwards compatibility
      {
        $and: [
          { gender: { $in: user.preferences.dating } },
          { 'preferences.gender': user.gender },
          { 'preferences.purpose': { $in: [undefined, 'dating'] } },
        ],
      },
      {
        $and: [
          { gender: { $in: user.preferences.friends } },
          { 'preferences.gender': user.gender },
          { 'preferences.purpose': { $in: [undefined, 'friends'] } },
        ],
      },
    ],
  };

  const preferenceFilters = {
    $and: [
      { 'personality.mbti': { $in: user.preferences.personality } },
      {
        'preferences.maxAge': {
          $gte: moment().diff(user.birthday, 'years'),
        },
      },
      {
        'preferences.minAge': {
          $lte: moment().diff(user.birthday, 'years'),
        },
      },
      {
        birthday: {
          $gte: new Date(moment().subtract(user.preferences.maxAge, 'years').toISOString()),
          $lte: new Date(moment().subtract(user.preferences.minAge, 'years').toISOString()),
        },
      },
    ],
  };

  const outputFieldNameToQueryMap = {
    numLocalUsers: { $and: [baseFilter] },
    numLocalUsersDAU: { $and: [baseFilter, dauFilter] },
    numLocalUsersTargetGender: { $and: [baseFilter, genderCompatibleFilter] },
    numLocalUsersTargetGenderMatchable: { $and: [baseFilter, genderCompatibleFilter, exclusionListFilter] },
    numLocalUsersActiveTargetGenderMatchable: { $and: [baseFilter, genderCompatibleFilter, exclusionListFilter, excludeInactiveFilter] },
    numLocalUsersActiveTargetGenderMatchableInPreferences: { $and: [baseFilter, genderCompatibleFilter, exclusionListFilter, excludeInactiveFilter, preferenceFilters] },
  };

  const results = {};
  for (const outputFieldName in outputFieldNameToQueryMap) {
    const query = outputFieldNameToQueryMap[outputFieldName];
    try {
      const result = await this.computeLocalStatsHelper(user, query, maxDistance, outputFieldName);
      let val = 0;
      if (result.length > 0) {
        val = result[0][outputFieldName];
      }
      results[`localStats.${outputFieldName}`] = val;
    } catch (exception) {
      console.log('Error in computeLocalStats: ', exception);
    }
  }

  try {
    await this.updateOne(
      { _id: userId },
      { $set: results },
    );
  } catch (exception) {
    console.log('Error in computeLocalStats: ', exception);
  }
};

userSchema.statics.computeLocalStatsHelper = async function (user, filterQuery, maxDistance, outputFieldName) {
  const filters = [
    {
      $geoNear: {
        near: user.location,
        distanceField: 'distance',
        minDistance: 0,
        maxDistance: maxDistance * 1609, // miles to meters
        query: filterQuery,
        spherical: true,
      },
    },
    { $count: outputFieldName },
  ];

  let timeoutHandle;
  const timeoutPromise = new Promise((_resolve, reject) => {
    timeoutHandle = setTimeout(
      () => reject(new Error('Timeout: computeLocalStatsHelper 5000ms')),
      5000,
    );
  });

  const asyncPromise = this.aggregate(filters);

  return Promise.race([asyncPromise, timeoutPromise]).then((result) => {
    clearTimeout(timeoutHandle);
    return result;
  });
};

async function removeChats(user) {
  // immediately delete pending chats with no messages
  {
    const result = await Chat.deleteMany({
      users: user,
      pending: true,
      numMessages: 0,
    });
    console.log(`Deleted pending chats for user ${user._id}: ${JSON.stringify(result)}`);
  }

  // mark remaining chats for deletion
  const chats = await Chat.find({ users: user, deletedAt: null }).lean();
  console.log(`Removing ${chats.length} chats for user ${user._id}`);

  for (const chat of chats) {
    if (chat.groupChat) {
      await Chat.removeFromGroupChat(
        chat._id,
        user._id,
        `${user.firstName} left the chat.`,
      );
    } else {
      await Chat.removeChatAndMessages(chat);

      // remove user from friends friendList
      for (friendId of chat.users) {
        if (friendId != user._id) {
          if(sendSocketEventForDeleteChat){
            sendSocketEventForDeleteChat(friendId, 'deleted chat', { _id: chat._id }); // only sending delete chat event for other users
          }
          await FriendList.removeFriendship(user._id, friendId);
        }
      }
    }
  }

  console.log(`Removed ${chats.length} chats for user ${user._id}`);
}

async function removePictures(user) {
  await s3.emptyS3Directory(`${user._id}/`, 'removeUserPictures');
  console.log(`Removed pictures from s3 for user ${user._id}`);
  await S3DeletionMetric.increment('removePicturesNumCalls', 1);
}

async function removePersonalityQuizResults(user) {
  const rv = await PersonalityQuizResult.updateMany({ user: user._id }, { $set: { user: null } });
  console.log(`Removed personality quiz results for user ${user._id}`, rv);
}

async function removeUserMetadata(user) {
  const rv = await UserMetadata.deleteOne({ user });
  console.log(`Removed metadata for user ${user._id}`, rv);
}

async function removeExclusionList(user) {
  const rv = await ExclusionList.deleteOne({ user });
  console.log(`Removed exclusion list for user ${user._id}`, rv);
}

async function removeActions(user) {
  let rv;
  rv = await Action.deleteMany({ from: user });
  console.log(`Removed all actions from user ${user._id}`, rv);
  rv = await Block.deleteMany({ from: user._id });
  console.log(`Removed all blocks from user ${user._id}`, rv);
  rv = await Action.deleteMany({ to: user });
  console.log(`Removed all actions to user ${user._id}`, rv);
}

async function dismissReports(user) {
  await Report.dismissReports({ reportedUser: user._id });
}

async function removeAllOtherData(user) {
  await Follow.removeAllFollows(user._id);
  await SavedQuestion.removeAllForUser(user._id);
  await Question.removeAllForUser(user._id);
  await Comment.removeAllForUser(user._id);
  await QuestionCandidate.removeAllForUser(user._id);
  await Translation.removeAllForUser(user._id);
  await FriendList.deleteOne( { userId: user._id } );
  await QuestionViewData.deleteMany({user: user._id});
  await Story.deleteMany({ createdBy: user._id });
  await ProfileView.deleteMany({ from: user._id });
  await ProfileView.deleteMany({ to: user._id });
  await InterestPoint.deleteMany({ user: user._id });
}

userSchema.methods.deleteAccount = async function () {
  const user = this;

  // delete associated data
  await removeChats(user);
  await removePictures(user);
  await removePersonalityQuizResults(user);
  await removeUserMetadata(user);
  await removeExclusionList(user);
  await removeActions(user);
  await removeAllOtherData(user);
  await dismissReports(user);

  // delete this user
  await user.deleteOne();

  // save an anonymized copy for metrics
  const deletedAccount = new DeletedAccount(user);
  deletedAccount.isNew = true;
  deletedAccount._id = undefined;
  deletedAccount.deletedDate = Date.now();
  await deletedAccount.save();
};

userSchema.methods.updateEvents = function (events) {
  const eventKeysToCheck = new Set(['open_power_popup', 'use_time_travel', 'open_boost_popup', 'use_boost', 'open_superlove_popup', 'use_superlove', 'open_filter_popup', 'use_filter', 'open_dm_popup', 'use_dm'])
  const firstUsageMetrics = this.firstUsageMetrics;
  const metrics = this.metrics
  for (const [key, value] of Object.entries(events)) {
    if (key in this.events) {
      if (!this.events[key]) {
        this.events[key] = 0;
      }
      this.events[key] += 1;
      if (this.events[key] === 1 && eventKeysToCheck.has(key) && this.createdAt > get_app_413_date()) {
        firstUsageMetrics[`${key}_numActionsSent`] = metrics.numActionsSent;
        firstUsageMetrics[`${key}_numLikesSent`] = metrics.numLikesSent;
        firstUsageMetrics[`${key}_numPassesSent`] = metrics.numPassesSent;
      }
    }
  }
  return this
};

userSchema.statics.updateSearchFields = async function (userId) {
  const {
    firstName,
    education,
    work,
    description,
    prompts,
  } = await this.findOne({ _id: userId }, {
    firstName: 1, education: 1, work: 1, description: 1, prompts: 1,
  });
  await this.updateOne(
    { _id: userId },
    {
      $set: {
        keywords: Array.from(new Set([
          ...extractValidWords(firstName || ''),
          ...extractValidWords(education || ''),
          ...extractValidWords(work || ''),
          ...extractValidWords(description || ''),
          ...extractValidWords(prompts.map((x) => x.answer).join(' ') || ''),
        ])),
      },
    },
  );
};

userSchema.statics.removeFcmToken = async function (userId, fcmToken) {
  await this.updateOne(
    {
      _id: userId,
      fcmToken,
    },
    {
      $set: {
        fcmToken: null,
        fcmTokenUpdatedAt: Date.now(),
      },
    },
  );
};

// Middleware
// =============================================================================

const profileFields = [
  'age',
  'viewableInDailyProfiles',
  'metrics.numPendingReports',
  'preferences',
  'incomingRequestsPreferences',
  'genderPreferenceHash',
  'personality',
  'countryCode',
  'latitude2',
  'longitude2',
  'interestNames',
  'enneagram',
  'relationshipStatus',
  'datingSubPreferences',
  'relationshipType',
  'languages',
  'horoscope',
  'keywords',
  'moreAboutUser',
  'verification',
  'education',
  'work',
  'description',
  'prompts',
  'height',
];

// Fields used for copy generation prompt
const copyGenerationFields = [
  'age',
  'gender',
  'description',
  'audioDescriptionTranscription',
  'prompts',
  'education',
  'work',
  'ethnicities',
  'moreAboutUser',
  'city',
  'state',
  'country',
  'interestNames',
  'preferences',
  'locale',
];

userSchema.pre('save', function (next) {
  if (this.modifiedPaths().some(f => profileFields.includes(f))) {
    this.profileModifiedAt = getProfileModifiedAt();
  }

  if (this.modifiedPaths().some(f => copyGenerationFields.includes(f))) {
    this.booInfinityCopy = undefined;
  }

  for (const key of ['localLoops', 'countryLoops', 'countryGroupLoops']) {
    // if these maps grow too large, remove keys in FIFO order
    if (this.modifiedPaths().includes(key)) {
      const map = this[key];
      for (const k of map.keys()) {
        if (map.size < 500) {
            break;
        }
        map.delete(k);
      }
    }
  }

  if (
    this.modifiedPaths().includes('preferences')
    || this.modifiedPaths().includes('preferencesModifiedAt')
    || this.modifiedPaths().includes('hideFromKeywords')
    || this.modifiedPaths().includes('hideFromNearby')
  ) {
    this.preferencesModifiedAt = new Date();
    this.recommendationsExhaustedAt = null;
    this.recentRecommendations = [];
    this.recentRecommendationsSavedAt = null;
    this.localLoops = {};
    this.countryLoops = {};
    this.countryGroupLoops = {};
    if(this.events.finished_signup && this.modifiedPaths().includes('preferences')) {
      this.updateEvents({ use_filter: true })
    }
  }
  if (this.modifiedPaths().includes('latitude2') || this.modifiedPaths().includes('longitude2')) {
    this.recentRecommendations = [];
    this.recentRecommendationsSavedAt = null;
    if (!this.preferences.global) {
      this.recommendationsExhaustedAt = null;
    }
  }
  if (this.modifiedPaths().includes('aiFilterPreference')) {
    this.recentRecommendations = [];
    this.recentRecommendationsSavedAt = null;
  }

  // interest loop in daily profiles algo is currently disabled
  /*
  if (this.modifiedPaths().includes('interestNames')) {
    this.localLoops = {};
    this.countryLoops = {};
    this.countryGroupLoops = {};
  }
  */

  if (this.preferences.distance2 && !this.versionAtLeast('1.11.78')) {
    this.preferences.local = true;
    if (this.preferences.distance2 == 100) {
      this.preferences.global = true;
    } else {
      this.preferences.global = false;
    }
  }

  if (this.modifiedPaths().includes('countryCode')) {
    this.region = getRegion(this.countryCode);
  }

  if (this.modifiedPaths().includes('preferences.distance2') || this.modifiedPaths().includes('preferences.distance') || this.modifiedPaths().includes('preferences.global') || this.modifiedPaths().includes('preferences.local')){
    this.minDistanceForCarrotAlgo = undefined
  }

  next();
});

const socialAttributes = [
  "age",
  "horoscope",
  "preferences",
  "genderPreferenceHash",
  "verification",
  "personality",
  "region",
  "countryCode",
  "city",
  "state",
  "latitude2",
  "longitude2",
  "enneagram",
];

//to update question userAttributes
const userAttributesMap={
  age:"userAttributes.age",
  horoscope:"userAttributes.horoscope",
  genderPreferenceHash:"userAttributes.genderPreferenceHash",
  countryCode:"userAttributes.countryCode",
  city:"userAttributes.city",
  state:"userAttributes.state",
  latitude2:"userAttributes.latitude2",
  longitude2:"userAttributes.longitude2",
  enneagram:"userAttributes.enneagram",
  region:"region",
};

userSchema.pre('save', async function (next) {
  const modifiedPaths = this.modifiedPaths();
    // modified paths for prompts, bio and pictures

  if (modifiedPaths.some(modifiedPath => ['prompts','description','pictures','originalPictures','locale'].includes(modifiedPath))) {
    this.fetchNewProfileAnalysis = true;
  }
  if (modifiedPaths.some(modifiedPath => socialAttributes.includes(modifiedPath))) {
    const updatedSocialAttributes = modifiedPaths.filter(modifiedPath => socialAttributes.includes(modifiedPath));
    const setQuery = {};
    if (updatedSocialAttributes.includes('preferences')) {
      setQuery['userAttributes.maxAge'] = this.preferences.maxAge;
      setQuery['userAttributes.minAge'] = this.preferences.minAge;
    }
    if (updatedSocialAttributes.includes('verification')) {
      setQuery['userAttributes.status'] = this.verification.status;
    }
    if (updatedSocialAttributes.includes('personality')) {
      setQuery['userAttributes.mbti'] = this.personality.mbti;
    }

    updatedSocialAttributes.forEach((updatedAttribute) => {
      const field = userAttributesMap[updatedAttribute];
      if (field) {
        setQuery[field] = this[updatedAttribute];
      }
    });
    await Question.updateMany({ createdBy: this._id }, { $set: setQuery });
  }
  next();
});




// Export schema
// =============================================================================
module.exports = mongoose.model('User', userSchema);
