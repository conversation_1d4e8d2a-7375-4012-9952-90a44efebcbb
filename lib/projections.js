const userProfileAdminFields = [
  '_id',
  'firstName',
  'pictures',
  'personality.mbti',
  'gender',
  'age',
  'height',
  'ethnicities',
  'description',
  'audioDescription',
  'audioDescriptionWaveform',
  'audioDescriptionDuration',
  'education',
  'work',
  'enneagram',
  'moreAboutUser',
  'prompts',
  'city',
  'state',
  'country',
  'horoscope',
  'interestNames',
  'karma',
  'metrics.numFollowers',
  'awards',
  'languages',
  'handle',
  'shadowBanned',
  'createdAt',
  'updatedAt',
  'deviceId',
  'timezone',
  'telepathyPreviewMbti',
  'actualCity',
  'actualState',
  'actualCountry',
  'verification',
  'livenessVerification',
];

const userProfileAdminFieldsObj = userProfileAdminFields.reduce((a, v) => ({ ...a, [v]: 1}), {});

function getBlockLookup(userId,createdBy='$createdBy') {
  return {
    $lookup: {
      from: 'blocks',
      let: {
        me: userId,
        createdBy: createdBy,
      },
      pipeline: [
        {
          $match:
          {
            $expr:
            {
              $or:
              [
                {
                  $and:
                  [
                    { $eq: ['$from', '$$me'] },
                    { $eq: ['$to', '$$createdBy'] },
                  ],
                },
                {
                  $and:
                  [
                    { $eq: ['$from', '$$createdBy'] },
                    { $eq: ['$to', '$$me'] },
                  ],
                },
              ],
            },
          },
        },
      ],
      as: 'block',
    },
  };
}

const fullProfileFields = [
  '_id',
  'firstName',
  'pictures',
  'personality',
  'gender',
  'birthday',
  'age',
  'height',
  'ethnicities',
  'description',
  'audioDescription',
  'audioDescriptionWaveform',
  'audioDescriptionDuration',
  'education',
  'work',
  'enneagram',
  'moreAboutUser',
  'prompts',
  'crown',
  'translator',
  'actualCountry',
  'admin',
  'premiumExpiration',
  'handle',
  'searchable',
  'location',
  'city',
  'state',
  'country',
  'countryCode',
  'hideCity',
  'hideLocation',
  'timezone',
  'teleportLocation',
  'preferences',
  'appVersion',
  'hideQuestions',
  'hideComments',
  'hideHoroscope',
  'hideMyFollowerCount',
  'hideMyAwards',
  'hideMyKarma',
  'hideMyAge',
  'updatedAt',
  'horoscope',
  'interests',
  'interestNames',
  'karma',
  'metrics.numFollowers',
  'verification',
  'awards',
  'languages',
  'hidden',
  'metrics.numPendingReports',
  'shadowBanned',
  'banned',
  'metrics.lastSeen',
  'hideReadReceipts',
  'interestPoints',
  'relationshipStatus',
  'datingSubPreferences',
  'relationshipType',
  'sexuality',
  'sexualityVisibility',
  'config',
  'showMyInfinityStatus',
  'scores.likeRatioScore',
  'scores.totalScore2',
  'createdAt',
  'metrics.lastSeen',
  'metrics.numActionsReceived',
  'privatePictures',
];

const fullProfileFieldsStr = fullProfileFields.join(' ');
const fullProfileFieldsObj = fullProfileFields.reduce((a, v) => ({ ...a, [v]: 1}), {});

const profilePreviewProjection = {
  _id: 1,
  firstName: 1,
  picture: { $first: '$pictures' },
  'personality.mbti': 1,
  enneagram: 1,
  horoscope: { $cond: [ '$hideHoroscope', null, '$horoscope' ] },
  gender: 1,
  age: { $cond: [{ $and: ['$hideMyAge', { $gt: ['$premiumExpiration', '$$NOW'] }] }, null, '$age'] },
  handle: 1,
  verified: { $eq: [ '$verification.status', 'verified' ] },
};

module.exports = {
  userProfileAdminFields,
  userProfileAdminFieldsObj,
  fullProfileFields,
  fullProfileFieldsStr,
  fullProfileFieldsObj,
  getBlockLookup,
  profilePreviewProjection,
};
